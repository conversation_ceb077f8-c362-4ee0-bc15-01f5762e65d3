version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: lumilove-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: lumilove
      MYSQL_USER: lumilove
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/src/main/resources/schema.sql:/docker-entrypoint-initdb.d/1-schema.sql
      - ./backend/src/main/resources/data.sql:/docker-entrypoint-initdb.d/2-data.sql
    networks:
      - lumilove-network

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: lumilove-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data
    networks:
      - lumilove-network

  # Backend Spring Boot Application
  backend:
    build:
      context: ./backend
      dockerfile: Dockerfile
    container_name: lumilove-backend
    environment:
      - SPRING_PROFILES_ACTIVE=docker
      - DB_HOST=mysql
      - DB_USERNAME=lumilove
      - DB_PASSWORD=password
      - REDIS_HOST=redis
    ports:
      - "8080:8080"
    depends_on:
      - mysql
      - redis
    networks:
      - lumilove-network
    volumes:
      - ./backend/.env:/app/.env

  # Frontend Next.js Application
  frontend:
    build:
      context: ./lumilove (1)
      dockerfile: Dockerfile
    container_name: lumilove-frontend
    environment:
      - NEXT_PUBLIC_API_URL=http://localhost:8080/api
    ports:
      - "3000:3000"
    depends_on:
      - backend
    networks:
      - lumilove-network

volumes:
  mysql_data:
  redis_data:

networks:
  lumilove-network:
    driver: bridge
