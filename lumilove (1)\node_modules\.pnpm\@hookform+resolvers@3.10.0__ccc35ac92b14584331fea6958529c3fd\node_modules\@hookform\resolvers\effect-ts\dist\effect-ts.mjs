import{toNestErrors as r,validateFieldsNatively as e}from"@hookform/resolvers";import{Effect as n}from"effect";import{decodeUnknown as o,ArrayFormatter as t}from"effect/ParseResult";var u=function(u,s){return void 0===s&&(s={errors:"all",onExcessProperty:"ignore"}),function(i,a,c){return o(u,s)(i).pipe(n.catchAll(function(r){return n.flip(t.formatIssue(r))}),n.mapError(function(e){var n=e.reduce(function(r,e){return r[e.path.join(".")]={message:e.message,type:e._tag},r},{});return r(n,c)}),n.tap(function(){return n.sync(function(){return c.shouldUseNativeValidation&&e({},c)})}),n.match({onFailure:function(r){return{errors:r,values:{}}},onSuccess:function(r){return{errors:{},values:r}}}),n.runPromise)}};export{u as effectTsResolver};
//# sourceMappingURL=effect-ts.module.js.map
