# 🚀 Lumilove 快速启动指南

## 🎯 现在就试用网站！

### 方法一：使用 Docker（推荐）

**前提条件：**
- 安装 Docker Desktop
- 确保 Docker 正在运行

**启动步骤：**
1. 双击运行 `start.bat`
2. 等待服务启动（约2-3分钟）
3. 打开浏览器访问：http://localhost:3000

### 方法二：开发模式

**前提条件：**
- Java 17+
- Node.js 18+
- Docker Desktop（用于数据库）

**启动步骤：**
1. 双击运行 `start-dev.bat`
2. 等待服务启动
3. 打开浏览器访问：http://localhost:3000

## 🔑 默认登录信息

- **用户名**: admin
- **密码**: admin123

## 🌟 主要功能试用

### 1. 浏览角色
- 首页可以看到男性和女性角色
- 点击标签筛选不同类型的角色
- 查看热门角色排行榜

### 2. 聊天功能
- 点击任意角色卡片进入聊天
- 发送文本消息（无限制）
- 尝试图片回复（免费用户限3次）
- 尝试语音回复（免费用户限3次）

### 3. 创建角色
- 访问 /create-lover 页面
- 自定义角色外观和性格
- 保存并与自己的角色聊天

### 4. 图片生成
- 访问 /create 页面
- 选择角色和场景
- 生成AI图片

### 5. 相册功能
- 访问 /album 页面
- 浏览图片包
- 体验高级内容预览

### 6. 会员功能
- 访问 /payment 页面
- 查看会员权益
- 体验订阅流程

## 🛠️ 如果遇到问题

### 常见问题解决

1. **端口被占用**
   ```bash
   # 停止所有服务
   docker-compose down
   
   # 检查端口占用
   netstat -ano | findstr :3000
   netstat -ano | findstr :8080
   ```

2. **数据库连接失败**
   ```bash
   # 重启数据库
   docker-compose restart mysql
   
   # 查看数据库日志
   docker-compose logs mysql
   ```

3. **前端无法连接后端**
   - 检查后端是否启动：http://localhost:8080/api/health
   - 检查环境变量配置

### 查看日志
```bash
# 查看所有服务日志
docker-compose logs -f

# 查看特定服务日志
docker-compose logs -f backend
docker-compose logs -f frontend
```

### 停止服务
```bash
# 停止所有Docker服务
docker-compose down

# 或者关闭开发模式的命令窗口
```

## 📱 访问地址

- **网站首页**: http://localhost:3000
- **聊天页面**: http://localhost:3000/chat/1
- **创建角色**: http://localhost:3000/create-lover
- **图片生成**: http://localhost:3000/create
- **相册**: http://localhost:3000/album
- **支付页面**: http://localhost:3000/payment
- **后端API**: http://localhost:8080/api

## 🎮 体验建议

1. **先注册新用户**或使用默认管理员账号
2. **浏览角色**，了解不同类型的AI伴侣
3. **开始聊天**，体验AI对话功能
4. **尝试图片和语音**回复（注意免费限制）
5. **创建自己的角色**，个性化体验
6. **生成图片**，看看AI创作能力
7. **查看会员功能**，了解完整功能

## 💡 提示

- 免费用户有图片和语音限制，可以体验会员升级流程
- 所有数据都存储在本地数据库中
- 可以创建多个用户账号测试不同功能
- AI回复目前使用模拟数据，实际部署时需要配置真实的AI API

## 🆘 需要帮助？

如果遇到任何问题：
1. 查看控制台错误信息
2. 检查Docker容器状态：`docker-compose ps`
3. 查看详细日志：`docker-compose logs -f`
4. 重启服务：`docker-compose restart`

享受您的Lumilove体验！ 🌟
