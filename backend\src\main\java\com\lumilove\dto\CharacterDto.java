package com.lumilove.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.List;

@Data
public class CharacterDto {
    private Long id;
    private String name;
    private String description;
    private String avatarUrl;
    private BigDecimal price;
    private Long creatorId;
    private Boolean isPublic;
    private Integer categoryId;
    private Integer usageCount;
    private Integer likeCount;
    private Boolean isNsfw;
    private String gender;
    private String occupation;
    private List<String> tags;
    private String chatCount;
    private String imageSrc;
    private LocalDateTime createdAt;
    private LocalDateTime updatedAt;
}
