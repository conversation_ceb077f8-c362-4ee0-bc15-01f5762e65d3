package com.lumilove.dto;

import lombok.Data;

import java.math.BigDecimal;
import java.time.LocalDateTime;

@Data
public class UserDto {
    private Long id;
    private String username;
    private String email;
    private BigDecimal balance;
    private Boolean isPremium;
    private LocalDateTime premiumExpiresAt;
    private Integer pictureQuota;
    private Integer voiceQuota;
    private String avatarUrl;
    private LocalDateTime lastLoginAt;
    private LocalDateTime createdAt;
}
