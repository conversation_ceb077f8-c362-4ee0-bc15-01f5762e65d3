-- Create database if not exists
CREATE DATABASE IF NOT EXISTS lumilove CHARACTER SET utf8mb4 COLLATE utf8mb4_unicode_ci;
USE lumilove;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(100) NOT NULL,
    balance DECIMAL(10,2) DEFAULT 0.00,
    is_premium BOOLEAN DEFAULT FALSE,
    premium_expires_at DATETIME NULL,
    picture_quota INT DEFAULT 3,
    voice_quota INT DEFAULT 3,
    avatar_url VARCHAR(255) NULL,
    is_active BOOLEAN DEFAULT TRUE,
    last_login_at DATETIME NULL,
    stripe_customer_id VARCHAR(255) NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Characters table
CREATE TABLE IF NOT EXISTS characters (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(100) NOT NULL,
    description TEXT,
    prompt_config JSON,
    avatar_url VARCHAR(255),
    price DECIMAL(10,2) DEFAULT 0.00,
    creator_id BIGINT NOT NULL,
    clone_source_id BIGINT NULL,
    is_public BOOLEAN DEFAULT FALSE,
    category_id INT NULL,
    voice_config JSON,
    image_generation_config JSON,
    usage_count INT DEFAULT 0,
    like_count INT DEFAULT 0,
    is_nsfw BOOLEAN DEFAULT FALSE,
    gender VARCHAR(10),
    occupation VARCHAR(50),
    tags JSON,
    chat_count VARCHAR(20) DEFAULT '0',
    image_src VARCHAR(255),
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (creator_id) REFERENCES users(id)
);

-- Categories table
CREATE TABLE IF NOT EXISTS categories (
    id INT PRIMARY KEY AUTO_INCREMENT,
    name VARCHAR(50) NOT NULL,
    parent_id INT NULL,
    description TEXT,
    is_active BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Chat history table
CREATE TABLE IF NOT EXISTS chat_history (
    id BIGINT PRIMARY KEY AUTO_INCREMENT,
    user_id BIGINT NOT NULL,
    character_id BIGINT NOT NULL,
    message TEXT NOT NULL,
    response TEXT NOT NULL,
    message_type VARCHAR(20) DEFAULT 'text',
    response_type VARCHAR(20) DEFAULT 'text',
    audio_url VARCHAR(255) NULL,
    image_url VARCHAR(255) NULL,
    audio_duration INT NULL,
    is_user_message BOOLEAN DEFAULT TRUE,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (character_id) REFERENCES characters(id)
);

-- Orders table
CREATE TABLE IF NOT EXISTS orders (
    id VARCHAR(32) PRIMARY KEY,
    user_id BIGINT NOT NULL,
    character_id BIGINT NULL,
    amount DECIMAL(10,2) NOT NULL,
    status ENUM('PENDING', 'PAID', 'EXPIRED', 'CANCELLED') DEFAULT 'PENDING',
    payment_gateway VARCHAR(20),
    currency VARCHAR(3) DEFAULT 'USD',
    refund_status ENUM('NONE', 'REQUESTED', 'COMPLETED') DEFAULT 'NONE',
    stripe_payment_intent_id VARCHAR(255) NULL,
    order_type VARCHAR(20),
    subscription_type VARCHAR(20),
    coins_amount INT NULL,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP,
    FOREIGN KEY (user_id) REFERENCES users(id),
    FOREIGN KEY (character_id) REFERENCES characters(id)
);

-- Create indexes for better performance
CREATE INDEX idx_characters_gender ON characters(gender);
CREATE INDEX idx_characters_public ON characters(is_public);
CREATE INDEX idx_characters_creator ON characters(creator_id);
CREATE INDEX idx_chat_history_user_character ON chat_history(user_id, character_id);
CREATE INDEX idx_chat_history_created_at ON chat_history(created_at);
CREATE INDEX idx_orders_user ON orders(user_id);
CREATE INDEX idx_orders_status ON orders(status);
