const API_BASE_URL = process.env.NEXT_PUBLIC_API_URL || 'http://localhost:8080/api';

// Types
export interface User {
  id: number;
  username: string;
  email: string;
  balance: number;
  isPremium: boolean;
  premiumExpiresAt?: string;
  pictureQuota: number;
  voiceQuota: number;
  avatarUrl?: string;
  lastLoginAt?: string;
  createdAt: string;
}

export interface Character {
  id: number;
  name: string;
  description: string;
  avatarUrl?: string;
  price: number;
  creatorId: number;
  isPublic: boolean;
  categoryId?: number;
  usageCount: number;
  likeCount: number;
  isNsfw: boolean;
  gender: string;
  occupation: string;
  tags: string[];
  chatCount: string;
  imageSrc?: string;
  createdAt: string;
  updatedAt: string;
}

export interface ChatMessage {
  id: number;
  message: string;
  response: string;
  messageType: string;
  responseType: string;
  audioUrl?: string;
  imageUrl?: string;
  audioDuration?: number;
  isUserMessage: boolean;
  timestamp: string;
}

export interface AuthResponse {
  token: string;
  type: string;
  user: User;
}

// API Client class
class ApiClient {
  private baseURL: string;
  private token: string | null = null;

  constructor(baseURL: string) {
    this.baseURL = baseURL;
    // Load token from localStorage if available
    if (typeof window !== 'undefined') {
      this.token = localStorage.getItem('auth_token');
    }
  }

  setToken(token: string) {
    this.token = token;
    if (typeof window !== 'undefined') {
      localStorage.setItem('auth_token', token);
    }
  }

  clearToken() {
    this.token = null;
    if (typeof window !== 'undefined') {
      localStorage.removeItem('auth_token');
    }
  }

  private async request<T>(
    endpoint: string,
    options: RequestInit = {}
  ): Promise<T> {
    const url = `${this.baseURL}${endpoint}`;
    const headers: HeadersInit = {
      'Content-Type': 'application/json',
      ...options.headers,
    };

    if (this.token) {
      headers.Authorization = `Bearer ${this.token}`;
    }

    const response = await fetch(url, {
      ...options,
      headers,
    });

    if (!response.ok) {
      const errorText = await response.text();
      throw new Error(`API Error: ${response.status} - ${errorText}`);
    }

    return response.json();
  }

  // Auth endpoints
  async login(usernameOrEmail: string, password: string): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/login', {
      method: 'POST',
      body: JSON.stringify({ usernameOrEmail, password }),
    });
    this.setToken(response.token);
    return response;
  }

  async register(username: string, email: string, password: string): Promise<AuthResponse> {
    const response = await this.request<AuthResponse>('/auth/register', {
      method: 'POST',
      body: JSON.stringify({ username, email, password }),
    });
    this.setToken(response.token);
    return response;
  }

  async getCurrentUser(): Promise<User> {
    return this.request<User>('/auth/me');
  }

  // Character endpoints
  async getPublicCharacters(page = 0, size = 20): Promise<Character[]> {
    return this.request<Character[]>(`/characters/public?page=${page}&size=${size}`);
  }

  async getCharactersByGender(gender: string): Promise<Character[]> {
    return this.request<Character[]>(`/characters/public/gender/${gender}`);
  }

  async getTrendingCharacters(limit = 10): Promise<Character[]> {
    return this.request<Character[]>(`/characters/public/trending?limit=${limit}`);
  }

  async searchCharacters(keyword: string): Promise<Character[]> {
    return this.request<Character[]>(`/characters/public/search?keyword=${encodeURIComponent(keyword)}`);
  }

  async getCharactersByTag(tag: string): Promise<Character[]> {
    return this.request<Character[]>(`/characters/public/tag/${encodeURIComponent(tag)}`);
  }

  async getCharacterById(id: number): Promise<Character> {
    return this.request<Character>(`/characters/${id}`);
  }

  async createCharacter(character: Partial<Character>): Promise<Character> {
    return this.request<Character>('/characters', {
      method: 'POST',
      body: JSON.stringify(character),
    });
  }

  async updateCharacter(id: number, character: Partial<Character>): Promise<Character> {
    return this.request<Character>(`/characters/${id}`, {
      method: 'PUT',
      body: JSON.stringify(character),
    });
  }

  async likeCharacter(id: number): Promise<void> {
    await this.request<void>(`/characters/${id}/like`, {
      method: 'POST',
    });
  }

  async getMyCharacters(): Promise<Character[]> {
    return this.request<Character[]>('/characters/my');
  }

  // Chat endpoints
  async getChatHistory(characterId: number, limit = 20): Promise<ChatMessage[]> {
    return this.request<ChatMessage[]>(`/chat/history/${characterId}?limit=${limit}`);
  }

  async getRecentChatCharacterIds(limit = 10): Promise<number[]> {
    return this.request<number[]>(`/chat/recent?limit=${limit}`);
  }

  async sendMessage(characterId: number, message: string, messageType = 'text', responseType = 'text'): Promise<string> {
    return this.request<string>('/chat/send', {
      method: 'POST',
      body: JSON.stringify({
        characterId,
        message,
        messageType,
        responseType,
      }),
    });
  }

  // Streaming chat
  createChatStream(characterId: number, message: string, messageType = 'text', responseType = 'text'): EventSource {
    const url = `${this.baseURL}/chat/stream`;
    const eventSource = new EventSource(url);
    
    // Send the message via POST first
    fetch(url, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
        'Authorization': `Bearer ${this.token}`,
      },
      body: JSON.stringify({
        characterId,
        message,
        messageType,
        responseType,
      }),
    });

    return eventSource;
  }

  logout() {
    this.clearToken();
  }
}

// Create and export the API client instance
export const apiClient = new ApiClient(API_BASE_URL);

// Export individual functions for convenience
export const {
  login,
  register,
  getCurrentUser,
  getPublicCharacters,
  getCharactersByGender,
  getTrendingCharacters,
  searchCharacters,
  getCharactersByTag,
  getCharacterById,
  createCharacter,
  updateCharacter,
  likeCharacter,
  getMyCharacters,
  getChatHistory,
  getRecentChatCharacterIds,
  sendMessage,
  createChatStream,
  logout,
} = apiClient;
