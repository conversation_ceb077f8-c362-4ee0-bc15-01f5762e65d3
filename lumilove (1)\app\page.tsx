"use client"

import Image from "next/image"
import Link from "next/link"
import { Search } from "lucide-react"
import Sidebar from "@/components/sidebar"
import CharacterCard from "@/components/character-card"
import TrendingList from "@/components/trending-list"
import { Button } from "@/components/ui/button"
import { Tabs, Tabs<PERSON>ist, TabsTrigger } from "@/components/ui/tabs"
import { Badge } from "@/components/ui/badge"
import { useState, useEffect } from "react"
import { useAuth } from "@/contexts/AuthContext"
import { apiClient, Character } from "@/lib/api"

export default function Home() {
  const { user } = useAuth()
  const [activeGender, setActiveGender] = useState<"guys" | "girls">("guys")
  const [activeTag, setActiveTag] = useState("For You")
  const [characters, setCharacters] = useState<Character[]>([])
  const [trendingCharacters, setTrendingCharacters] = useState<Character[]>([])
  const [recentChatIds, setRecentChatIds] = useState<number[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    loadCharacters()
    loadTrendingCharacters()
    if (user) {
      loadRecentChats()
    }
  }, [activeGender, user])

  const loadCharacters = async () => {
    try {
      setLoading(true)
      const data = await apiClient.getCharactersByGender(activeGender === "guys" ? "male" : "female")
      setCharacters(data)
    } catch (error) {
      console.error("Failed to load characters:", error)
    } finally {
      setLoading(false)
    }
  }

  const loadTrendingCharacters = async () => {
    try {
      const data = await apiClient.getTrendingCharacters(5)
      setTrendingCharacters(data)
    } catch (error) {
      console.error("Failed to load trending characters:", error)
    }
  }

  const loadRecentChats = async () => {
    try {
      const ids = await apiClient.getRecentChatCharacterIds(3)
      setRecentChatIds(ids)
    } catch (error) {
      console.error("Failed to load recent chats:", error)
    }
  }

  // Filter characters based on active tag
  const filteredCharacters = activeTag === "For You"
    ? characters
    : characters.filter((char) => char.tags.includes(activeTag))

  // Get recent chat characters
  const recentChats = characters.filter(char => recentChatIds.includes(char.id)).slice(0, 3)

  // Filter tags
  const maleFilterTags = [
    "For You",
    "Popular",
    "Alpha",
    "Mysterious",
    "Adventurous",
    "Anti-Hero",
    "Mafia",
    "Cold Exterior",
    "Witty",
    "Werewolf",
    "Billionaire",
    "Badboy",
    "Mature",
    "Gentle",
  ]

  const femaleFilterTags = [
    "For You",
    "Popular",
    "Mysterious",
    "Girl-Next-Door",
    "Independent",
    "Playful",
    "Smart",
    "Caring",
    "Brave",
    "Elegant",
    "Nurturing",
    "Teacher",
    "Nurse",
    "Cheerleader",
    "Lawyer",
    "Seductive",
  ]

  // Determine if we should show recent chats
  const hasRecentChats = user && recentChats.length > 0

  return (
    <div className="flex min-h-screen">
      <Sidebar />
      <main className="flex-1 overflow-auto">
        <div className="px-2 py-6">
          {/* Top Navigation */}
          <div className="flex items-center justify-between mb-8">
            <div className="relative flex-1 max-w-xl">
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <Search className="h-5 w-5 text-gray-400" />
              </div>
              <input
                type="text"
                placeholder="Search Your Favorite AI"
                className="w-full pl-10 pr-4 py-3 rounded-full bg-[#1a0a24] border border-[#3a1a44] focus:outline-none focus:ring-2 focus:ring-pink-400 text-base"
              />
            </div>
            <div className="flex items-center space-x-4">
              <Link href="/premium">
                <div className="flex items-center space-x-2 bg-[#1a0a24] px-3 py-1 rounded-full text-sm hover:bg-[#2a1a34] transition-colors cursor-pointer">
                  <div className="h-8 w-8 rounded-full overflow-hidden">
                    <Image src="/placeholder.svg?height=32&width=32&text=U" alt="User" width={32} height={32} />
                  </div>
                  <div className="bg-[#2a1a34] px-3 py-1 rounded-full text-sm">Free Plan ↗</div>
                </div>
              </Link>
            </div>
          </div>

          {/* Recent Chats Section (only shown if user has recent chats) */}
          {hasRecentChats && (
            <div className="mb-8">
              <h2 className="text-xl font-semibold mb-3">Recent chat</h2>
              <div className="flex space-x-5">
                {recentChats.map((chat) => (
                  <Link href={`/chat/${chat.id}`} key={chat.id} className="text-center">
                    <div className="h-20 w-20 rounded-full overflow-hidden mx-auto mb-2 border-2 border-pink-500">
                      <Image
                        src={chat.imageSrc || chat.avatarUrl || "/placeholder.svg"}
                        alt={chat.name}
                        width={80}
                        height={80}
                        className="object-cover"
                      />
                    </div>
                    <span className="text-base">{chat.name}</span>
                  </Link>
                ))}
              </div>
            </div>
          )}

          {/* Hero Banner */}
          <div className="relative rounded-xl overflow-hidden mb-8 gradient-bg">
            <div className="flex flex-col md:flex-row items-center p-6 md:p-10">
              <div className="md:w-1/2 mb-6 md:mb-0">
                <h2 className="text-3xl md:text-5xl lg:text-6xl font-bold mb-4">Create Your Ideal AI Lover</h2>
                <p className="text-gray-300 mb-6 max-w-lg text-lg">
                  Start creating your ideal AI lover today! Customize every detail, bring them to life, and dive into
                  unforgettable stories together.
                </p>
                <Link href="/create">
                  <Button className="bg-pink-500 hover:bg-pink-600 text-white px-8 py-6 h-auto rounded-full text-lg">
                    <span className="mr-2">+</span> START CREATING NOW
                  </Button>
                </Link>
              </div>
              <div className="md:w-1/2 relative h-56 md:h-80 overflow-hidden rounded-lg">
                <div className="grid grid-cols-3 gap-2 h-full">
                  {[1, 2, 3, 4, 5, 6].map((i) => (
                    <div key={i} className="overflow-hidden">
                      <Image
                        src={`/placeholder.svg?height=160&width=120&text=${i}`}
                        alt="AI character"
                        width={120}
                        height={160}
                        className="object-cover h-full w-full"
                      />
                    </div>
                  ))}
                </div>
              </div>
            </div>
          </div>

          {/* Gender Tabs */}
          <Tabs
            defaultValue="guys"
            className="mb-6"
            onValueChange={(value) => setActiveGender(value as "guys" | "girls")}
          >
            <TabsList className="bg-[#1a0a24] p-1 rounded-full w-fit">
              <TabsTrigger value="girls" className="rounded-full px-8 py-2.5 text-base data-[state=active]:bg-pink-500">
                👧 Girls
              </TabsTrigger>
              <TabsTrigger value="guys" className="rounded-full px-8 py-2.5 text-base data-[state=active]:bg-pink-500">
                👦 Guys
              </TabsTrigger>
            </TabsList>
          </Tabs>

          {/* Filter Tags */}
          <div className="flex overflow-x-auto pb-4 mb-6 scrollbar-hide">
            <div className="flex space-x-3">
              {(activeGender === "guys" ? maleFilterTags : femaleFilterTags).map((tag, index) => (
                <Badge
                  key={index}
                  variant={tag === activeTag ? "default" : "outline"}
                  className={`whitespace-nowrap px-5 py-2.5 text-base rounded-full cursor-pointer ${
                    tag === activeTag ? "bg-pink-500 hover:bg-pink-600" : "bg-[#1a0a24] hover:bg-[#2a1a34]"
                  }`}
                  onClick={() => setActiveTag(tag)}
                >
                  {tag === "For You" && <span className="mr-1">⭐</span>}
                  {tag === "Popular" && <span className="mr-1">🔥</span>}
                  {tag}
                </Badge>
              ))}
            </div>
          </div>

          {/* Main Content */}
          <div className="flex flex-col md:flex-row gap-4">
            {/* Character Cards */}
            <div className="md:w-3/4">
              {loading ? (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {[...Array(6)].map((_, i) => (
                    <div key={i} className="bg-[#1a0a24] rounded-xl p-4 animate-pulse">
                      <div className="h-48 bg-gray-700 rounded-lg mb-4"></div>
                      <div className="h-4 bg-gray-700 rounded mb-2"></div>
                      <div className="h-3 bg-gray-700 rounded mb-2"></div>
                      <div className="h-3 bg-gray-700 rounded w-1/2"></div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
                  {filteredCharacters.map((character) => (
                    <CharacterCard key={character.id} character={character} />
                  ))}
                </div>
              )}
            </div>

            {/* Trending Sidebar */}
            <div className="md:w-1/4">
              <TrendingList characters={trendingCharacters} />
            </div>
          </div>
        </div>
      </main>
    </div>
  )
}
