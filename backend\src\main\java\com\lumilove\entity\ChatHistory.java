package com.lumilove.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

@Data
@Entity
@Table(name = "chat_history")
@EqualsAndHashCode(callSuper = true)
public class ChatHistory extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "character_id", nullable = false)
    private Long characterId;
    
    @Column(columnDefinition = "TEXT", nullable = false)
    private String message;
    
    @Column(columnDefinition = "TEXT", nullable = false)
    private String response;
    
    @Column(name = "message_type")
    private String messageType = "text"; // text, picture, voice
    
    @Column(name = "response_type")
    private String responseType = "text"; // text, picture, voice
    
    @Column(name = "audio_url")
    private String audioUrl;
    
    @Column(name = "image_url")
    private String imageUrl;
    
    @Column(name = "audio_duration")
    private Integer audioDuration;
    
    @Column(name = "is_user_message")
    private Boolean isUserMessage = true;
    
    // Foreign key relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "character_id", insertable = false, updatable = false)
    private Character character;
}
