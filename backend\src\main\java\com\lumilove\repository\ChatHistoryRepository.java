package com.lumilove.repository;

import com.lumilove.entity.ChatHistory;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface ChatHistoryRepository extends JpaRepository<ChatHistory, Long> {
    
    List<ChatHistory> findByUserIdAndCharacterIdOrderByCreatedAtDesc(Long userId, Long characterId);
    
    List<ChatHistory> findByUserIdAndCharacterIdOrderByCreatedAtDesc(Long userId, Long characterId, Pageable pageable);
    
    @Query("SELECT ch FROM ChatHistory ch WHERE ch.userId = :userId AND ch.characterId = :characterId " +
           "ORDER BY ch.createdAt DESC")
    List<ChatHistory> findRecentChatHistory(@Param("userId") Long userId, 
                                          @Param("characterId") Long characterId, 
                                          Pageable pageable);
    
    @Query("SELECT DISTINCT ch.characterId FROM ChatHistory ch WHERE ch.userId = :userId " +
           "ORDER BY MAX(ch.createdAt) DESC")
    List<Long> findRecentChatCharacterIds(@Param("userId") Long userId, Pageable pageable);
    
    @Query("SELECT ch FROM ChatHistory ch WHERE ch.userId = :userId " +
           "ORDER BY ch.createdAt DESC")
    List<ChatHistory> findByUserIdOrderByCreatedAtDesc(@Param("userId") Long userId, Pageable pageable);
}
