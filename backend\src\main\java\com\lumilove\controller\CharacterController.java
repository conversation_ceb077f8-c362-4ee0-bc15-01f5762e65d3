package com.lumilove.controller;

import com.lumilove.dto.CharacterDto;
import com.lumilove.entity.Character;
import com.lumilove.entity.User;
import com.lumilove.service.CharacterService;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@RestController
@RequestMapping("/characters")
@RequiredArgsConstructor
public class CharacterController {
    
    private final CharacterService characterService;
    
    @GetMapping("/public")
    public ResponseEntity<List<CharacterDto>> getPublicCharacters(
            @RequestParam(defaultValue = "0") int page,
            @RequestParam(defaultValue = "20") int size) {
        
        Pageable pageable = PageRequest.of(page, size);
        Page<Character> characters = characterService.getPublicCharacters(pageable);
        
        List<CharacterDto> characterDtos = characters.getContent().stream()
                .map(characterService::convertToDto)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(characterDtos);
    }
    
    @GetMapping("/public/gender/{gender}")
    public ResponseEntity<List<CharacterDto>> getCharactersByGender(@PathVariable String gender) {
        List<Character> characters = characterService.getCharactersByGender(gender);
        List<CharacterDto> characterDtos = characters.stream()
                .map(characterService::convertToDto)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(characterDtos);
    }
    
    @GetMapping("/public/trending")
    public ResponseEntity<List<CharacterDto>> getTrendingCharacters(
            @RequestParam(defaultValue = "10") int limit) {
        
        List<Character> characters = characterService.getTrendingCharacters(limit);
        List<CharacterDto> characterDtos = characters.stream()
                .map(characterService::convertToDto)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(characterDtos);
    }
    
    @GetMapping("/public/search")
    public ResponseEntity<List<CharacterDto>> searchCharacters(@RequestParam String keyword) {
        List<Character> characters = characterService.searchCharacters(keyword);
        List<CharacterDto> characterDtos = characters.stream()
                .map(characterService::convertToDto)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(characterDtos);
    }
    
    @GetMapping("/public/tag/{tag}")
    public ResponseEntity<List<CharacterDto>> getCharactersByTag(@PathVariable String tag) {
        List<Character> characters = characterService.getCharactersByTag(tag);
        List<CharacterDto> characterDtos = characters.stream()
                .map(characterService::convertToDto)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(characterDtos);
    }
    
    @GetMapping("/{id}")
    public ResponseEntity<CharacterDto> getCharacterById(@PathVariable Long id) {
        Character character = characterService.getCharacterById(id)
                .orElseThrow(() -> new RuntimeException("Character not found"));
        
        CharacterDto characterDto = characterService.convertToDto(character);
        return ResponseEntity.ok(characterDto);
    }
    
    @PostMapping
    public ResponseEntity<CharacterDto> createCharacter(
            @RequestBody CharacterDto characterDto,
            Authentication authentication) {
        
        User user = (User) authentication.getPrincipal();
        Character character = characterService.createCharacter(characterDto, user.getId());
        CharacterDto responseDto = characterService.convertToDto(character);
        
        return ResponseEntity.ok(responseDto);
    }
    
    @PutMapping("/{id}")
    public ResponseEntity<CharacterDto> updateCharacter(
            @PathVariable Long id,
            @RequestBody CharacterDto characterDto,
            Authentication authentication) {
        
        User user = (User) authentication.getPrincipal();
        Character character = characterService.updateCharacter(id, characterDto, user.getId());
        CharacterDto responseDto = characterService.convertToDto(character);
        
        return ResponseEntity.ok(responseDto);
    }
    
    @PostMapping("/{id}/like")
    public ResponseEntity<Void> likeCharacter(@PathVariable Long id) {
        characterService.incrementLikes(id);
        return ResponseEntity.ok().build();
    }
    
    @GetMapping("/my")
    public ResponseEntity<List<CharacterDto>> getMyCharacters(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        List<Character> characters = characterService.getCharactersByCreator(user.getId());
        List<CharacterDto> characterDtos = characters.stream()
                .map(characterService::convertToDto)
                .collect(Collectors.toList());
        
        return ResponseEntity.ok(characterDtos);
    }
}
