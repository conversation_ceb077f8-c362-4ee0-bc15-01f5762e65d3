package com.lumilove.controller;

import com.lumilove.dto.AuthRequest;
import com.lumilove.dto.AuthResponse;
import com.lumilove.dto.UserDto;
import com.lumilove.entity.User;
import com.lumilove.security.JwtUtil;
import com.lumilove.service.UserService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.ResponseEntity;
import org.springframework.security.authentication.AuthenticationManager;
import org.springframework.security.authentication.UsernamePasswordAuthenticationToken;
import org.springframework.security.core.Authentication;
import org.springframework.security.core.AuthenticationException;
import org.springframework.web.bind.annotation.*;

@Slf4j
@RestController
@RequestMapping("/auth")
@RequiredArgsConstructor
public class AuthController {
    
    private final AuthenticationManager authenticationManager;
    private final UserService userService;
    private final JwtUtil jwtUtil;
    
    @PostMapping("/login")
    public ResponseEntity<?> login(@Valid @RequestBody AuthRequest authRequest) {
        try {
            Authentication authentication = authenticationManager.authenticate(
                new UsernamePasswordAuthenticationToken(
                    authRequest.getUsernameOrEmail(),
                    authRequest.getPassword()
                )
            );
            
            User user = (User) authentication.getPrincipal();
            user = userService.updateLastLogin(user);
            
            String token = jwtUtil.generateToken(user);
            UserDto userDto = userService.convertToDto(user);
            
            return ResponseEntity.ok(new AuthResponse(token, userDto));
            
        } catch (AuthenticationException e) {
            log.error("Authentication failed for user: {}", authRequest.getUsernameOrEmail(), e);
            return ResponseEntity.badRequest().body("Invalid username/email or password");
        }
    }
    
    @PostMapping("/register")
    public ResponseEntity<?> register(@Valid @RequestBody RegisterRequest registerRequest) {
        try {
            User user = userService.createUser(
                registerRequest.getUsername(),
                registerRequest.getEmail(),
                registerRequest.getPassword()
            );
            
            String token = jwtUtil.generateToken(user);
            UserDto userDto = userService.convertToDto(user);
            
            return ResponseEntity.ok(new AuthResponse(token, userDto));
            
        } catch (RuntimeException e) {
            log.error("Registration failed", e);
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }
    
    @GetMapping("/me")
    public ResponseEntity<UserDto> getCurrentUser(Authentication authentication) {
        User user = (User) authentication.getPrincipal();
        UserDto userDto = userService.convertToDto(user);
        return ResponseEntity.ok(userDto);
    }
}

class RegisterRequest {
    private String username;
    private String email;
    private String password;
    
    // Getters and setters
    public String getUsername() { return username; }
    public void setUsername(String username) { this.username = username; }
    
    public String getEmail() { return email; }
    public void setEmail(String email) { this.email = email; }
    
    public String getPassword() { return password; }
    public void setPassword(String password) { this.password = password; }
}
