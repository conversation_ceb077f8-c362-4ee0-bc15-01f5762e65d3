{"version": 3, "sources": ["../src/index.ts", "../src/VisuallyHidden.tsx"], "sourcesContent": ["export {\n  VisuallyHidden,\n  //\n  Root,\n} from './VisuallyHidden';\nexport type { VisuallyHiddenProps } from './VisuallyHidden';\n", "import * as React from 'react';\nimport { Primitive } from '@radix-ui/react-primitive';\n\n/* -------------------------------------------------------------------------------------------------\n * VisuallyHidden\n * -----------------------------------------------------------------------------------------------*/\n\nconst NAME = 'VisuallyHidden';\n\ntype VisuallyHiddenElement = React.ElementRef<typeof Primitive.span>;\ntype PrimitiveSpanProps = React.ComponentPropsWithoutRef<typeof Primitive.span>;\ninterface VisuallyHiddenProps extends PrimitiveSpanProps {}\n\nconst VisuallyHidden = React.forwardRef<VisuallyHiddenElement, VisuallyHiddenProps>(\n  (props, forwardedRef) => {\n    return (\n      <Primitive.span\n        {...props}\n        ref={forwardedRef}\n        style={{\n          // See: https://github.com/twbs/bootstrap/blob/main/scss/mixins/_visually-hidden.scss\n          position: 'absolute',\n          border: 0,\n          width: 1,\n          height: 1,\n          padding: 0,\n          margin: -1,\n          overflow: 'hidden',\n          clip: 'rect(0, 0, 0, 0)',\n          whiteSpace: 'nowrap',\n          wordWrap: 'normal',\n          ...props.style,\n        }}\n      />\n    );\n  }\n);\n\nVisuallyHidden.displayName = NAME;\n\n/* -----------------------------------------------------------------------------------------------*/\n\nconst Root = VisuallyHidden;\n\nexport {\n  VisuallyHidden,\n  //\n  Root,\n};\nexport type { VisuallyHiddenProps };\n"], "mappings": ";;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAAA;AAAA;AAAA;AAAA;AAAA;AAAA;;;ACAA,YAAuB;AACvB,6BAA0B;AAepB;AATN,IAAM,OAAO;AAMb,IAAM,iBAAuB;AAAA,EAC3B,CAAC,OAAO,iBAAiB;AACvB,WACE;AAAA,MAAC,iCAAU;AAAA,MAAV;AAAA,QACE,GAAG;AAAA,QACJ,KAAK;AAAA,QACL,OAAO;AAAA;AAAA,UAEL,UAAU;AAAA,UACV,QAAQ;AAAA,UACR,OAAO;AAAA,UACP,QAAQ;AAAA,UACR,SAAS;AAAA,UACT,QAAQ;AAAA,UACR,UAAU;AAAA,UACV,MAAM;AAAA,UACN,YAAY;AAAA,UACZ,UAAU;AAAA,UACV,GAAG,MAAM;AAAA,QACX;AAAA;AAAA,IACF;AAAA,EAEJ;AACF;AAEA,eAAe,cAAc;AAI7B,IAAM,OAAO;", "names": []}