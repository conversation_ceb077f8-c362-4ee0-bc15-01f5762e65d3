package com.lumilove.dto;

import jakarta.validation.constraints.NotBlank;
import jakarta.validation.constraints.NotNull;
import lombok.Data;

@Data
public class ChatRequest {
    
    @NotNull(message = "Character ID is required")
    private Long characterId;
    
    @NotBlank(message = "Message is required")
    private String message;
    
    private String messageType = "text"; // text, picture, voice
    
    private String responseType = "text"; // text, picture, voice
}
