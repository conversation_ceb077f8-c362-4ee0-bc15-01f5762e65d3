### Lumilove Website - Interaction Logic Documentation

## Overview

Lumilove is an AI companion platform that allows users to chat with virtual characters, generate images, and access premium content through a subscription model. This document outlines the interaction logic and user flows throughout the application.

## Navigation Structure

The application uses a sidebar for main navigation with the following sections:

- Home/Dashboard
- Chat
- Album/Gallery
- Sneaky (premium content)
- Create (character/image generation)
- Payment


## User Flows and Interaction Logic

### 1. Main Navigation (Sidebar)

- **Sidebar Component**: Provides navigation throughout the application
- **Interaction Logic**:

- Clicking navigation items routes users to respective pages
- "Become Premium" button directs users to the payment page
- Responsive design collapses on mobile devices





### 2. Chat System

#### Chat List

- **Location**: Left sidebar in chat interface
- **Interaction Logic**:

- Displays recent conversations with characters
- Unread indicators show new messages
- Clicking a chat opens the conversation with that character





#### Chat Interface

- **Interaction Logic**:

- Text input allows sending messages to characters
- Three response types available:

- Text (unlimited)
- Picture (limited to 3 for free users)
- Voice (limited to 3 for free users)



- When quota is exceeded, subscription modal appears
- Quick reply suggestions provide common phrases
- Audio messages can be played/paused





#### Character Profile

- **Interaction Logic**:

- Clicking character avatar opens profile modal
- Profile displays character information, images, and background
- CTA buttons direct to payment page for premium features
- Share button opens sharing dialog with social media options





#### Call Feature

- **Interaction Logic**:

- Phone icon initiates a simulated call with the character
- Free calls limited to 1 minute
- Call displays subtitles that animate as if the character is speaking
- Call controls include end call, mute, and heart animation
- After 60 seconds, call ends and subscription modal appears





#### Recommendation System

- **Interaction Logic**:

- After several messages, recommendation bubble appears
- Suggests another character the user might be interested in
- Clicking "Meet them now" redirects to that character's chat





### 3. Image Generation (Create Page)

- **Interaction Logic**:

- Character selection from recent chats or upload custom image
- Scene, clothing, pose, and angle selection via modal dialogs
- "Random" button automatically selects random options
- Image count selection (1, 4, or 9 images)

- Multiple images require premium subscription



- Generate button creates images based on selections
- Generated images can be downloaded





### 4. Character Creation (Create Lover Page)

- **Interaction Logic**:

- Two-step process (appearance and personality)
- Gender selection affects available options
- Art style selection (realistic, anime, hybrid)
- Optional reference image upload
- AI Writer button generates random descriptions
- Next button advances to personality creation (not fully implemented)





### 5. Sneaky Gallery

#### Gallery List

- **Interaction Logic**:

- Displays available photo packs
- Clicking a pack opens detailed gallery view





#### Gallery Detail

- **Interaction Logic**:

- Grid display of images in the pack
- Locked images show premium overlay
- Clicking unlocked images opens fullscreen view
- Fullscreen view includes navigation and action buttons
- Premium CTA section directs to payment page





### 6. Payment System

- **Interaction Logic**:

- Displays premium benefits
- Two subscription options:

- Monthly ($9.99 first month, then $19.99)
- Annual ($59.99 first year, then $119.99)



- Payment method selection (credit card or wallet)
- Subscription modal appears when:

- User exceeds free quota for premium features
- User clicks premium CTAs throughout the app
- User clicks "Become Premium" in sidebar



- All payment-related links direct to the payment page





### 7. Subscription Modal

- **Interaction Logic**:

- Appears when attempting to use premium features
- Lists premium benefits
- Displays limited-time discount
- Provides subscription options
- "Maybe Later" button dismisses the modal





## Premium Features and Limitations

### Free User Limitations

- Limited to 3 picture requests in chat
- Limited to 3 voice messages in chat
- Limited to 1-minute calls
- Cannot access locked images in galleries
- Limited to single image generation (not 4 or 9)


### Premium Benefits

- Unlimited image generation
- Unlimited voice messages
- Unlimited calls
- Access to all locked content
- Ability to create custom characters


## State Management

- **Local State Management**:

- React useState hooks manage component-level state
- useRef for DOM references and timers
- useEffect for side effects and cleanup



- **Key State Variables**:

- User quotas (picture, voice)
- Subscription status
- Chat messages
- Selected character
- Media playback state
- Modal visibility





## Responsive Design

- Desktop, tablet, and mobile layouts
- Sidebar collapses on mobile
- Chat interface adapts to screen size
- Gallery grid adjusts columns based on viewport


## Error Handling

- Graceful degradation for missing images
- Fallback content for unavailable characters
- Quota management to prevent exceeding limits


## Future Implementation Notes

The following features are referenced in the UI but not fully implemented:

- User authentication system
- Payment processing backend
- Character creation backend
- AI chat backend integration
- Voice generation
- Video call capability


## Entry Points to Payment Page

- Sidebar "Become Premium" button
- "Unlock" buttons on locked gallery images
- "Subscribe Now" buttons in premium CTAs
- Subscription buttons in profile modals
- Subscription modal after exceeding free quotas
- Subscription modal after 1-minute calls


This document provides a comprehensive overview of the interaction logic in the Lumilove website, covering all major user flows and features.