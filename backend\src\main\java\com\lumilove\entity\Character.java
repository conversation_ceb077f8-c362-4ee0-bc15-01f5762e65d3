package com.lumilove.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@Entity
@Table(name = "characters")
@EqualsAndHashCode(callSuper = true)
public class Character extends BaseEntity {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(nullable = false, length = 100)
    private String name;
    
    @Column(columnDefinition = "TEXT")
    private String description;
    
    @Column(name = "prompt_config", columnDefinition = "JSON")
    private String promptConfig;
    
    @Column(name = "avatar_url")
    private String avatarUrl;
    
    @Column(precision = 10, scale = 2)
    private BigDecimal price = BigDecimal.ZERO;
    
    @Column(name = "creator_id", nullable = false)
    private Long creatorId;
    
    @Column(name = "clone_source_id")
    private Long cloneSourceId;
    
    @Column(name = "is_public")
    private Boolean isPublic = false;
    
    @Column(name = "category_id")
    private Integer categoryId;
    
    @Column(name = "voice_config", columnDefinition = "JSON")
    private String voiceConfig;
    
    @Column(name = "image_generation_config", columnDefinition = "JSON")
    private String imageGenerationConfig;
    
    @Column(name = "usage_count")
    private Integer usageCount = 0;
    
    @Column(name = "like_count")
    private Integer likeCount = 0;
    
    @Column(name = "is_nsfw")
    private Boolean isNsfw = false;
    
    @Column(length = 10)
    private String gender;
    
    @Column(length = 50)
    private String occupation;
    
    @Column(name = "tags", columnDefinition = "JSON")
    private String tags;
    
    @Column(name = "chat_count")
    private String chatCount = "0";
    
    @Column(name = "image_src")
    private String imageSrc;
    
    // Helper methods
    public void incrementUsage() {
        this.usageCount++;
    }
    
    public void incrementLikes() {
        this.likeCount++;
    }
}
