package com.lumilove.service;

import com.fasterxml.jackson.core.type.TypeReference;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumilove.dto.CharacterDto;
import com.lumilove.entity.Character;
import com.lumilove.repository.CharacterRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.Optional;

@Slf4j
@Service
@RequiredArgsConstructor
public class CharacterService {
    
    private final CharacterRepository characterRepository;
    private final ObjectMapper objectMapper;
    
    public List<Character> getAllPublicCharacters() {
        return characterRepository.findByIsPublicTrue();
    }
    
    public Page<Character> getPublicCharacters(Pageable pageable) {
        return characterRepository.findByIsPublicTrue(pageable);
    }
    
    public List<Character> getCharactersByGender(String gender) {
        return characterRepository.findByGender(gender);
    }
    
    public List<Character> getTrendingCharacters(int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return characterRepository.findTrendingCharacters(pageable);
    }
    
    public List<Character> searchCharacters(String keyword) {
        return characterRepository.searchCharacters(keyword);
    }
    
    public List<Character> getCharactersByTag(String tag) {
        return characterRepository.findByTag(tag);
    }
    
    public Optional<Character> getCharacterById(Long id) {
        return characterRepository.findById(id);
    }
    
    public List<Character> getCharactersByCreator(Long creatorId) {
        return characterRepository.findByCreatorId(creatorId);
    }
    
    @Transactional
    public Character createCharacter(CharacterDto characterDto, Long creatorId) {
        Character character = new Character();
        character.setName(characterDto.getName());
        character.setDescription(characterDto.getDescription());
        character.setAvatarUrl(characterDto.getAvatarUrl());
        character.setPrice(characterDto.getPrice());
        character.setCreatorId(creatorId);
        character.setIsPublic(characterDto.getIsPublic());
        character.setCategoryId(characterDto.getCategoryId());
        character.setIsNsfw(characterDto.getIsNsfw());
        character.setGender(characterDto.getGender());
        character.setOccupation(characterDto.getOccupation());
        character.setImageSrc(characterDto.getImageSrc());
        
        // Convert tags list to JSON string
        if (characterDto.getTags() != null) {
            try {
                character.setTags(objectMapper.writeValueAsString(characterDto.getTags()));
            } catch (Exception e) {
                log.error("Error converting tags to JSON", e);
            }
        }
        
        return characterRepository.save(character);
    }
    
    @Transactional
    public Character updateCharacter(Long id, CharacterDto characterDto, Long userId) {
        Character character = characterRepository.findById(id)
                .orElseThrow(() -> new RuntimeException("Character not found"));
        
        // Check if user is the creator
        if (!character.getCreatorId().equals(userId)) {
            throw new RuntimeException("Not authorized to update this character");
        }
        
        character.setName(characterDto.getName());
        character.setDescription(characterDto.getDescription());
        character.setAvatarUrl(characterDto.getAvatarUrl());
        character.setPrice(characterDto.getPrice());
        character.setIsPublic(characterDto.getIsPublic());
        character.setCategoryId(characterDto.getCategoryId());
        character.setIsNsfw(characterDto.getIsNsfw());
        character.setGender(characterDto.getGender());
        character.setOccupation(characterDto.getOccupation());
        character.setImageSrc(characterDto.getImageSrc());
        
        if (characterDto.getTags() != null) {
            try {
                character.setTags(objectMapper.writeValueAsString(characterDto.getTags()));
            } catch (Exception e) {
                log.error("Error converting tags to JSON", e);
            }
        }
        
        return characterRepository.save(character);
    }
    
    @Transactional
    public void incrementUsage(Long characterId) {
        Character character = characterRepository.findById(characterId)
                .orElseThrow(() -> new RuntimeException("Character not found"));
        character.incrementUsage();
        characterRepository.save(character);
    }
    
    @Transactional
    public void incrementLikes(Long characterId) {
        Character character = characterRepository.findById(characterId)
                .orElseThrow(() -> new RuntimeException("Character not found"));
        character.incrementLikes();
        characterRepository.save(character);
    }
    
    public CharacterDto convertToDto(Character character) {
        CharacterDto dto = new CharacterDto();
        dto.setId(character.getId());
        dto.setName(character.getName());
        dto.setDescription(character.getDescription());
        dto.setAvatarUrl(character.getAvatarUrl());
        dto.setPrice(character.getPrice());
        dto.setCreatorId(character.getCreatorId());
        dto.setIsPublic(character.getIsPublic());
        dto.setCategoryId(character.getCategoryId());
        dto.setUsageCount(character.getUsageCount());
        dto.setLikeCount(character.getLikeCount());
        dto.setIsNsfw(character.getIsNsfw());
        dto.setGender(character.getGender());
        dto.setOccupation(character.getOccupation());
        dto.setChatCount(character.getChatCount());
        dto.setImageSrc(character.getImageSrc());
        dto.setCreatedAt(character.getCreatedAt());
        dto.setUpdatedAt(character.getUpdatedAt());
        
        // Convert JSON tags to list
        if (character.getTags() != null) {
            try {
                List<String> tags = objectMapper.readValue(character.getTags(), new TypeReference<List<String>>() {});
                dto.setTags(tags);
            } catch (Exception e) {
                log.error("Error parsing tags JSON", e);
            }
        }
        
        return dto;
    }
}
