server:
  port: 8080
  servlet:
    context-path: /api

spring:
  application:
    name: lumilove-backend
  
  datasource:
    url: ***********************************************************************************************************
    username: ${DB_USERNAME:root}
    password: ${DB_PASSWORD:password}
    driver-class-name: com.mysql.cj.jdbc.Driver
    
  jpa:
    hibernate:
      ddl-auto: update
    show-sql: false
    properties:
      hibernate:
        dialect: org.hibernate.dialect.MySQL8Dialect
        format_sql: true
        
  redis:
    host: ${REDIS_HOST:localhost}
    port: ${REDIS_PORT:6379}
    password: ${REDIS_PASSWORD:}
    database: 0
    timeout: 2000ms
    lettuce:
      pool:
        max-active: 8
        max-idle: 8
        min-idle: 0
        
  servlet:
    multipart:
      max-file-size: 10MB
      max-request-size: 10MB

# JWT Configuration
jwt:
  secret: ${JWT_SECRET:lumilove-secret-key-2024-very-long-and-secure}
  expiration: 86400000 # 24 hours

# Aliyun OSS Configuration
aliyun:
  oss:
    endpoint: ${OSS_ENDPOINT:https://oss-cn-hangzhou.aliyuncs.com}
    access-key-id: ${OSS_ACCESS_KEY_ID:your-access-key}
    access-key-secret: ${OSS_ACCESS_KEY_SECRET:your-access-secret}
    bucket-name: ${OSS_BUCKET_NAME:lumilove-bucket}
    
# AI Service Configuration
ai:
  gemini:
    api-key: ${GEMINI_API_KEY:your-gemini-api-key}
    base-url: https://generativelanguage.googleapis.com/v1beta
  
  image-generation:
    api-key: ${IMAGE_GEN_API_KEY:your-image-gen-api-key}
    base-url: ${IMAGE_GEN_BASE_URL:https://api.replicate.com}
    
  voice:
    minimax:
      api-key: ${MINIMAX_API_KEY:your-minimax-api-key}
      base-url: https://api.minimax.chat

# Stripe Configuration
stripe:
  public-key: ${STRIPE_PUBLIC_KEY:pk_test_your_public_key}
  secret-key: ${STRIPE_SECRET_KEY:sk_test_your_secret_key}
  webhook-secret: ${STRIPE_WEBHOOK_SECRET:whsec_your_webhook_secret}

# CORS Configuration
cors:
  allowed-origins: ${CORS_ALLOWED_ORIGINS:http://localhost:3000,http://localhost:3001}
  allowed-methods: GET,POST,PUT,DELETE,OPTIONS
  allowed-headers: "*"
  allow-credentials: true

# Logging
logging:
  level:
    com.lumilove: DEBUG
    org.springframework.security: DEBUG
  pattern:
    console: "%d{yyyy-MM-dd HH:mm:ss} - %msg%n"
    file: "%d{yyyy-MM-dd HH:mm:ss} [%thread] %-5level %logger{36} - %msg%n"
  file:
    name: logs/lumilove.log
