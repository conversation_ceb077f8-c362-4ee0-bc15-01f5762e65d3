@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 328 73% 69%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 240 4.8% 95.9%;
    --accent-foreground: 240 5.9% 10%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 328 73% 69%;
    --radius: 0.5rem;
  }

  .dark {
    --background: 270 70% 5%;
    --foreground: 0 0% 98%;
    --card: 270 70% 7%;
    --card-foreground: 0 0% 98%;
    --popover: 270 70% 7%;
    --popover-foreground: 0 0% 98%;
    --primary: 328 73% 69%;
    --primary-foreground: 355.7 100% 97.3%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 270 70% 11%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 270 70% 15%;
    --accent-foreground: 0 0% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 270 70% 15%;
    --input: 270 70% 15%;
    --ring: 328 73% 69%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

.gradient-bg {
  background: radial-gradient(circle at center, rgba(255, 105, 180, 0.3) 0%, rgba(14, 3, 20, 0.5) 70%);
}

.card-hover-effect {
  transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.card-hover-effect:hover {
  transform: translateY(-5px);
  box-shadow: 0 10px 25px -5px rgba(255, 105, 180, 0.4);
}
