@echo off
title Lumilove Startup Helper
color 0A

echo ========================================
echo    Lu<PERSON>love AI Platform Startup
echo ========================================
echo.

echo [1/5] Checking environment...

REM Check Docker
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker not found. Please install Docker Desktop first.
    echo Download from: https://www.docker.com/products/docker-desktop
    pause
    exit /b 1
) else (
    echo ✅ Docker found
)

REM Check Java
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java not found. Please install Java 17+
    echo Download from: https://adoptium.net/
    pause
    exit /b 1
) else (
    echo ✅ Java found
)

REM Check Node.js
node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js not found. Please install Node.js 18+
    echo Download from: https://nodejs.org/
    pause
    exit /b 1
) else (
    echo ✅ Node.js found
)

echo.
echo [2/5] Starting database and Redis...
docker-compose -f docker-compose.simple.yml down >nul 2>&1
docker-compose -f docker-compose.simple.yml up -d

if %errorlevel% neq 0 (
    echo ❌ Failed to start database. Please check Docker Desktop is running.
    pause
    exit /b 1
)

echo ✅ Database and Redis started

echo.
echo [3/5] Waiting for database to be ready...
timeout /t 20 /nobreak >nul
echo ✅ Database should be ready now

echo.
echo [4/5] Starting backend...
cd backend
start "Lumilove Backend" cmd /k "echo Starting backend... && .\mvnw.cmd spring-boot:run"
cd ..

echo ✅ Backend starting in new window

echo.
echo [5/5] Starting frontend...
cd "lumilove (1)"

REM Check if pnpm is installed
pnpm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo Installing pnpm...
    npm install -g pnpm
)

start "Lumilove Frontend" cmd /k "echo Installing dependencies and starting frontend... && pnpm install && pnpm dev"
cd ..

echo ✅ Frontend starting in new window

echo.
echo ========================================
echo           🎉 STARTUP COMPLETE!
echo ========================================
echo.
echo 📱 Website: http://localhost:3000
echo 🔧 Backend: http://localhost:8080/api
echo.
echo 🔑 Login credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo 📋 Two new windows should have opened:
echo    - Backend (Spring Boot)
echo    - Frontend (Next.js)
echo.
echo ⏳ Please wait 1-2 minutes for services to fully start
echo.
echo 🛑 To stop everything:
echo    1. Close the Backend and Frontend windows
echo    2. Run: docker-compose -f docker-compose.simple.yml down
echo.

pause
