{"version": 3, "file": "embla-carousel.cjs.js", "sources": ["../src/components/utils.ts", "../src/components/Alignment.ts", "../src/components/EventStore.ts", "../src/components/Animations.ts", "../src/components/Axis.ts", "../src/components/Limit.ts", "../src/components/Counter.ts", "../src/components/DragHandler.ts", "../src/components/DragTracker.ts", "../src/components/NodeRects.ts", "../src/components/PercentOfView.ts", "../src/components/ResizeHandler.ts", "../src/components/ScrollBody.ts", "../src/components/ScrollBounds.ts", "../src/components/ScrollContain.ts", "../src/components/ScrollLimit.ts", "../src/components/ScrollLooper.ts", "../src/components/ScrollProgress.ts", "../src/components/ScrollSnaps.ts", "../src/components/SlideRegistry.ts", "../src/components/ScrollTarget.ts", "../src/components/ScrollTo.ts", "../src/components/SlideFocus.ts", "../src/components/Vector1d.ts", "../src/components/Translate.ts", "../src/components/SlideLooper.ts", "../src/components/SlidesHandler.ts", "../src/components/SlidesInView.ts", "../src/components/SlideSizes.ts", "../src/components/SlidesToScroll.ts", "../src/components/Engine.ts", "../src/components/EventHandler.ts", "../src/components/Options.ts", "../src/components/OptionsHandler.ts", "../src/components/PluginsHandler.ts", "../src/components/EmblaCarousel.ts"], "sourcesContent": ["import { PointerEventType } from './DragTracker'\n\nexport type WindowType = Window & typeof globalThis\n\nexport function isNumber(subject: unknown): subject is number {\n  return typeof subject === 'number'\n}\n\nexport function isString(subject: unknown): subject is string {\n  return typeof subject === 'string'\n}\n\nexport function isBoolean(subject: unknown): subject is boolean {\n  return typeof subject === 'boolean'\n}\n\nexport function isObject(subject: unknown): subject is Record<string, unknown> {\n  return Object.prototype.toString.call(subject) === '[object Object]'\n}\n\nexport function mathAbs(n: number): number {\n  return Math.abs(n)\n}\n\nexport function mathSign(n: number): number {\n  return Math.sign(n)\n}\n\nexport function deltaAbs(valueB: number, valueA: number): number {\n  return mathAbs(valueB - valueA)\n}\n\nexport function factorAbs(valueB: number, valueA: number): number {\n  if (valueB === 0 || valueA === 0) return 0\n  if (mathAbs(valueB) <= mathAbs(valueA)) return 0\n  const diff = deltaAbs(mathAbs(valueB), mathAbs(valueA))\n  return mathAbs(diff / valueB)\n}\n\nexport function roundToTwoDecimals(num: number): number {\n  return Math.round(num * 100) / 100\n}\n\nexport function arrayKeys<Type>(array: Type[]): number[] {\n  return objectKeys(array).map(Number)\n}\n\nexport function arrayLast<Type>(array: Type[]): Type {\n  return array[arrayLastIndex(array)]\n}\n\nexport function arrayLastIndex<Type>(array: Type[]): number {\n  return Math.max(0, array.length - 1)\n}\n\nexport function arrayIsLastIndex<Type>(array: Type[], index: number): boolean {\n  return index === arrayLastIndex(array)\n}\n\nexport function arrayFromNumber(n: number, startAt: number = 0): number[] {\n  return Array.from(Array(n), (_, i) => startAt + i)\n}\n\nexport function objectKeys<Type extends object>(object: Type): string[] {\n  return Object.keys(object)\n}\n\nexport function objectsMergeDeep(\n  objectA: Record<string, unknown>,\n  objectB: Record<string, unknown>\n): Record<string, unknown> {\n  return [objectA, objectB].reduce((mergedObjects, currentObject) => {\n    objectKeys(currentObject).forEach((key) => {\n      const valueA = mergedObjects[key]\n      const valueB = currentObject[key]\n      const areObjects = isObject(valueA) && isObject(valueB)\n\n      mergedObjects[key] = areObjects\n        ? objectsMergeDeep(valueA, valueB)\n        : valueB\n    })\n    return mergedObjects\n  }, {})\n}\n\nexport function isMouseEvent(\n  evt: PointerEventType,\n  ownerWindow: WindowType\n): evt is MouseEvent {\n  return (\n    typeof ownerWindow.MouseEvent !== 'undefined' &&\n    evt instanceof ownerWindow.MouseEvent\n  )\n}\n", "import { isString } from './utils'\n\nexport type AlignmentOptionType =\n  | 'start'\n  | 'center'\n  | 'end'\n  | ((viewSize: number, snapSize: number, index: number) => number)\n\nexport type AlignmentType = {\n  measure: (n: number, index: number) => number\n}\n\nexport function Alignment(\n  align: AlignmentOptionType,\n  viewSize: number\n): AlignmentType {\n  const predefined = { start, center, end }\n\n  function start(): number {\n    return 0\n  }\n\n  function center(n: number): number {\n    return end(n) / 2\n  }\n\n  function end(n: number): number {\n    return viewSize - n\n  }\n\n  function measure(n: number, index: number): number {\n    if (isString(align)) return predefined[align](n)\n    return align(viewSize, n, index)\n  }\n\n  const self: AlignmentType = {\n    measure\n  }\n  return self\n}\n", "type EventNameType = keyof DocumentEventMap | keyof WindowEventMap\ntype EventHandlerType = (evt: any) => void\ntype EventOptionsType = boolean | AddEventListenerOptions | undefined\ntype EventRemoverType = () => void\n\nexport type EventStoreType = {\n  add: (\n    node: EventTarget,\n    type: EventNameType,\n    handler: EventHandlerType,\n    options?: EventOptionsType\n  ) => EventStoreType\n  clear: () => void\n}\n\nexport function EventStore(): EventStoreType {\n  let listeners: EventRemoverType[] = []\n\n  function add(\n    node: EventTarget,\n    type: EventNameType,\n    handler: EventHandlerType,\n    options: EventOptionsType = { passive: true }\n  ): EventStoreType {\n    let removeListener: EventRemoverType\n\n    if ('addEventListener' in node) {\n      node.addEventListener(type, handler, options)\n      removeListener = () => node.removeEventListener(type, handler, options)\n    } else {\n      const legacyMediaQueryList = <MediaQueryList>node\n      legacyMediaQueryList.addListener(handler)\n      removeListener = () => legacyMediaQueryList.removeListener(handler)\n    }\n\n    listeners.push(removeListener)\n    return self\n  }\n\n  function clear(): void {\n    listeners = listeners.filter((remove) => remove())\n  }\n\n  const self: EventStoreType = {\n    add,\n    clear\n  }\n  return self\n}\n", "import { EngineType } from './Engine'\nimport { EventStore } from './EventStore'\nimport { WindowType } from './utils'\n\nexport type AnimationsUpdateType = (engine: EngineType) => void\nexport type AnimationsRenderType = (engine: EngineType, alpha: number) => void\n\nexport type AnimationsType = {\n  init: () => void\n  destroy: () => void\n  start: () => void\n  stop: () => void\n  update: () => void\n  render: (alpha: number) => void\n}\n\nexport function Animations(\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  update: () => void,\n  render: (alpha: number) => void\n): AnimationsType {\n  const documentVisibleHandler = EventStore()\n  const fixedTimeStep = 1000 / 60\n\n  let lastTimeStamp: number | null = null\n  let accumulatedTime = 0\n  let animationId = 0\n\n  function init(): void {\n    documentVisibleHandler.add(ownerDocument, 'visibilitychange', () => {\n      if (ownerDocument.hidden) reset()\n    })\n  }\n\n  function destroy(): void {\n    stop()\n    documentVisibleHandler.clear()\n  }\n\n  function animate(timeStamp: DOMHighResTimeStamp): void {\n    if (!animationId) return\n    if (!lastTimeStamp) lastTimeStamp = timeStamp\n\n    const timeElapsed = timeStamp - lastTimeStamp\n    lastTimeStamp = timeStamp\n    accumulatedTime += timeElapsed\n\n    while (accumulatedTime >= fixedTimeStep) {\n      update()\n      accumulatedTime -= fixedTimeStep\n    }\n\n    const alpha = accumulatedTime / fixedTimeStep\n    render(alpha)\n\n    if (animationId) {\n      animationId = ownerWindow.requestAnimationFrame(animate)\n    }\n  }\n\n  function start(): void {\n    if (animationId) return\n    animationId = ownerWindow.requestAnimationFrame(animate)\n  }\n\n  function stop(): void {\n    ownerWindow.cancelAnimationFrame(animationId)\n    lastTimeStamp = null\n    accumulatedTime = 0\n    animationId = 0\n  }\n\n  function reset(): void {\n    lastTimeStamp = null\n    accumulatedTime = 0\n  }\n\n  const self: AnimationsType = {\n    init,\n    destroy,\n    start,\n    stop,\n    update,\n    render\n  }\n  return self\n}\n", "import { NodeRectType } from './NodeRects'\n\nexport type AxisOptionType = 'x' | 'y'\nexport type AxisDirectionOptionType = 'ltr' | 'rtl'\ntype AxisEdgeType = 'top' | 'right' | 'bottom' | 'left'\n\nexport type AxisType = {\n  scroll: AxisOptionType\n  cross: AxisOptionType\n  startEdge: AxisEdgeType\n  endEdge: AxisEdgeType\n  measureSize: (nodeRect: NodeRectType) => number\n  direction: (n: number) => number\n}\n\nexport function Axis(\n  axis: AxisOptionType,\n  contentDirection: AxisDirectionOptionType\n): AxisType {\n  const isRightToLeft = contentDirection === 'rtl'\n  const isVertical = axis === 'y'\n  const scroll = isVertical ? 'y' : 'x'\n  const cross = isVertical ? 'x' : 'y'\n  const sign = !isVertical && isRightToLeft ? -1 : 1\n  const startEdge = getStartEdge()\n  const endEdge = getEndEdge()\n\n  function measureSize(nodeRect: NodeRectType): number {\n    const { height, width } = nodeRect\n    return isVertical ? height : width\n  }\n\n  function getStartEdge(): AxisEdgeType {\n    if (isVertical) return 'top'\n    return isRightToLeft ? 'right' : 'left'\n  }\n\n  function getEndEdge(): AxisEdgeType {\n    if (isVertical) return 'bottom'\n    return isRightToLeft ? 'left' : 'right'\n  }\n\n  function direction(n: number): number {\n    return n * sign\n  }\n\n  const self: AxisType = {\n    scroll,\n    cross,\n    startEdge,\n    endEdge,\n    measureSize,\n    direction\n  }\n  return self\n}\n", "import { mathAbs } from './utils'\n\nexport type LimitType = {\n  min: number\n  max: number\n  length: number\n  constrain: (n: number) => number\n  reachedAny: (n: number) => boolean\n  reachedMax: (n: number) => boolean\n  reachedMin: (n: number) => boolean\n  removeOffset: (n: number) => number\n}\n\nexport function Limit(min: number = 0, max: number = 0): LimitType {\n  const length = mathAbs(min - max)\n\n  function reachedMin(n: number): boolean {\n    return n < min\n  }\n\n  function reachedMax(n: number): boolean {\n    return n > max\n  }\n\n  function reachedAny(n: number): boolean {\n    return reachedMin(n) || reachedMax(n)\n  }\n\n  function constrain(n: number): number {\n    if (!reachedAny(n)) return n\n    return reachedMin(n) ? min : max\n  }\n\n  function removeOffset(n: number): number {\n    if (!length) return n\n    return n - length * Math.ceil((n - max) / length)\n  }\n\n  const self: LimitType = {\n    length,\n    max,\n    min,\n    constrain,\n    reachedAny,\n    reachedMax,\n    reachedMin,\n    removeOffset\n  }\n  return self\n}\n", "import { Limit } from './Limit'\nimport { mathAbs } from './utils'\n\nexport type CounterType = {\n  get: () => number\n  set: (n: number) => CounterType\n  add: (n: number) => CounterType\n  clone: () => CounterType\n}\n\nexport function Counter(\n  max: number,\n  start: number,\n  loop: boolean\n): CounterType {\n  const { constrain } = Limit(0, max)\n  const loopEnd = max + 1\n  let counter = withinLimit(start)\n\n  function withinLimit(n: number): number {\n    return !loop ? constrain(n) : mathAbs((loopEnd + n) % loopEnd)\n  }\n\n  function get(): number {\n    return counter\n  }\n\n  function set(n: number): CounterType {\n    counter = withinLimit(n)\n    return self\n  }\n\n  function add(n: number): CounterType {\n    return clone().set(get() + n)\n  }\n\n  function clone(): CounterType {\n    return Counter(max, get(), loop)\n  }\n\n  const self: CounterType = {\n    get,\n    set,\n    add,\n    clone\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { AnimationsType } from './Animations'\nimport { CounterType } from './Counter'\nimport { DragTrackerType, PointerEventType } from './DragTracker'\nimport { EventHandlerType } from './EventHandler'\nimport { AxisType } from './Axis'\nimport { EventStore } from './EventStore'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollTargetType } from './ScrollTarget'\nimport { ScrollToType } from './ScrollTo'\nimport { Vector1DType } from './Vector1d'\nimport { PercentOfViewType } from './PercentOfView'\nimport { Limit } from './Limit'\nimport {\n  deltaAbs,\n  factorAbs,\n  isBoolean,\n  isMouseEvent,\n  mathAbs,\n  mathSign,\n  WindowType\n} from './utils'\n\ntype DragHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  evt: PointerEventType\n) => boolean | void\n\nexport type DragHandlerOptionType = boolean | DragHandlerCallbackType\n\nexport type DragHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n  pointerDown: () => boolean\n}\n\nexport function DragHandler(\n  axis: AxisType,\n  rootNode: HTMLElement,\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  target: Vector1DType,\n  dragTracker: DragTrackerType,\n  location: Vector1DType,\n  animation: AnimationsType,\n  scrollTo: ScrollToType,\n  scrollBody: ScrollBodyType,\n  scrollTarget: ScrollTargetType,\n  index: CounterType,\n  eventHandler: EventHandlerType,\n  percentOfView: PercentOfViewType,\n  dragFree: boolean,\n  dragThreshold: number,\n  skipSnaps: boolean,\n  baseFriction: number,\n  watchDrag: DragHandlerOptionType\n): DragHandlerType {\n  const { cross: crossAxis, direction } = axis\n  const focusNodes = ['INPUT', 'SELECT', 'TEXTAREA']\n  const nonPassiveEvent = { passive: false }\n  const initEvents = EventStore()\n  const dragEvents = EventStore()\n  const goToNextThreshold = Limit(50, 225).constrain(percentOfView.measure(20))\n  const snapForceBoost = { mouse: 300, touch: 400 }\n  const freeForceBoost = { mouse: 500, touch: 600 }\n  const baseSpeed = dragFree ? 43 : 25\n\n  let isMoving = false\n  let startScroll = 0\n  let startCross = 0\n  let pointerIsDown = false\n  let preventScroll = false\n  let preventClick = false\n  let isMouse = false\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchDrag) return\n\n    function downIfAllowed(evt: PointerEventType): void {\n      if (isBoolean(watchDrag) || watchDrag(emblaApi, evt)) down(evt)\n    }\n\n    const node = rootNode\n    initEvents\n      .add(node, 'dragstart', (evt) => evt.preventDefault(), nonPassiveEvent)\n      .add(node, 'touchmove', () => undefined, nonPassiveEvent)\n      .add(node, 'touchend', () => undefined)\n      .add(node, 'touchstart', downIfAllowed)\n      .add(node, 'mousedown', downIfAllowed)\n      .add(node, 'touchcancel', up)\n      .add(node, 'contextmenu', up)\n      .add(node, 'click', click, true)\n  }\n\n  function destroy(): void {\n    initEvents.clear()\n    dragEvents.clear()\n  }\n\n  function addDragEvents(): void {\n    const node = isMouse ? ownerDocument : rootNode\n    dragEvents\n      .add(node, 'touchmove', move, nonPassiveEvent)\n      .add(node, 'touchend', up)\n      .add(node, 'mousemove', move, nonPassiveEvent)\n      .add(node, 'mouseup', up)\n  }\n\n  function isFocusNode(node: Element): boolean {\n    const nodeName = node.nodeName || ''\n    return focusNodes.includes(nodeName)\n  }\n\n  function forceBoost(): number {\n    const boost = dragFree ? freeForceBoost : snapForceBoost\n    const type = isMouse ? 'mouse' : 'touch'\n    return boost[type]\n  }\n\n  function allowedForce(force: number, targetChanged: boolean): number {\n    const next = index.add(mathSign(force) * -1)\n    const baseForce = scrollTarget.byDistance(force, !dragFree).distance\n\n    if (dragFree || mathAbs(force) < goToNextThreshold) return baseForce\n    if (skipSnaps && targetChanged) return baseForce * 0.5\n\n    return scrollTarget.byIndex(next.get(), 0).distance\n  }\n\n  function down(evt: PointerEventType): void {\n    const isMouseEvt = isMouseEvent(evt, ownerWindow)\n    isMouse = isMouseEvt\n    preventClick = dragFree && isMouseEvt && !evt.buttons && isMoving\n    isMoving = deltaAbs(target.get(), location.get()) >= 2\n\n    if (isMouseEvt && evt.button !== 0) return\n    if (isFocusNode(evt.target as Element)) return\n\n    pointerIsDown = true\n    dragTracker.pointerDown(evt)\n    scrollBody.useFriction(0).useDuration(0)\n    target.set(location)\n    addDragEvents()\n    startScroll = dragTracker.readPoint(evt)\n    startCross = dragTracker.readPoint(evt, crossAxis)\n    eventHandler.emit('pointerDown')\n  }\n\n  function move(evt: PointerEventType): void {\n    const isTouchEvt = !isMouseEvent(evt, ownerWindow)\n    if (isTouchEvt && evt.touches.length >= 2) return up(evt)\n\n    const lastScroll = dragTracker.readPoint(evt)\n    const lastCross = dragTracker.readPoint(evt, crossAxis)\n    const diffScroll = deltaAbs(lastScroll, startScroll)\n    const diffCross = deltaAbs(lastCross, startCross)\n\n    if (!preventScroll && !isMouse) {\n      if (!evt.cancelable) return up(evt)\n      preventScroll = diffScroll > diffCross\n      if (!preventScroll) return up(evt)\n    }\n    const diff = dragTracker.pointerMove(evt)\n    if (diffScroll > dragThreshold) preventClick = true\n\n    scrollBody.useFriction(0.3).useDuration(0.75)\n    animation.start()\n    target.add(direction(diff))\n    evt.preventDefault()\n  }\n\n  function up(evt: PointerEventType): void {\n    const currentLocation = scrollTarget.byDistance(0, false)\n    const targetChanged = currentLocation.index !== index.get()\n    const rawForce = dragTracker.pointerUp(evt) * forceBoost()\n    const force = allowedForce(direction(rawForce), targetChanged)\n    const forceFactor = factorAbs(rawForce, force)\n    const speed = baseSpeed - 10 * forceFactor\n    const friction = baseFriction + forceFactor / 50\n\n    preventScroll = false\n    pointerIsDown = false\n    dragEvents.clear()\n    scrollBody.useDuration(speed).useFriction(friction)\n    scrollTo.distance(force, !dragFree)\n    isMouse = false\n    eventHandler.emit('pointerUp')\n  }\n\n  function click(evt: MouseEvent): void {\n    if (preventClick) {\n      evt.stopPropagation()\n      evt.preventDefault()\n      preventClick = false\n    }\n  }\n\n  function pointerDown(): boolean {\n    return pointerIsDown\n  }\n\n  const self: DragHandlerType = {\n    init,\n    destroy,\n    pointerDown\n  }\n  return self\n}\n", "import { AxisOptionType, AxisType } from './Axis'\nimport { isMouseEvent, mathAbs, WindowType } from './utils'\n\ntype PointerCoordType = keyof Touch | keyof MouseEvent\nexport type PointerEventType = TouchEvent | MouseEvent\n\nexport type DragTrackerType = {\n  pointerDown: (evt: PointerEventType) => number\n  pointerMove: (evt: PointerEventType) => number\n  pointerUp: (evt: PointerEventType) => number\n  readPoint: (evt: PointerEventType, evtAxis?: AxisOptionType) => number\n}\n\nexport function DragTracker(\n  axis: AxisType,\n  ownerWindow: WindowType\n): DragTrackerType {\n  const logInterval = 170\n\n  let startEvent: PointerEventType\n  let lastEvent: PointerEventType\n\n  function readTime(evt: PointerEventType): number {\n    return evt.timeStamp\n  }\n\n  function readPoint(evt: PointerEventType, evtAxis?: AxisOptionType): number {\n    const property = evtAxis || axis.scroll\n    const coord: PointerCoordType = `client${property === 'x' ? 'X' : 'Y'}`\n    return (isMouseEvent(evt, ownerWindow) ? evt : evt.touches[0])[coord]\n  }\n\n  function pointerDown(evt: PointerEventType): number {\n    startEvent = evt\n    lastEvent = evt\n    return readPoint(evt)\n  }\n\n  function pointerMove(evt: PointerEventType): number {\n    const diff = readPoint(evt) - readPoint(lastEvent)\n    const expired = readTime(evt) - readTime(startEvent) > logInterval\n\n    lastEvent = evt\n    if (expired) startEvent = evt\n    return diff\n  }\n\n  function pointerUp(evt: PointerEventType): number {\n    if (!startEvent || !lastEvent) return 0\n    const diffDrag = readPoint(lastEvent) - readPoint(startEvent)\n    const diffTime = readTime(evt) - readTime(startEvent)\n    const expired = readTime(evt) - readTime(lastEvent) > logInterval\n    const force = diffDrag / diffTime\n    const isFlick = diffTime && !expired && mathAbs(force) > 0.1\n\n    return isFlick ? force : 0\n  }\n\n  const self: DragTrackerType = {\n    pointerDown,\n    pointerMove,\n    pointerUp,\n    readPoint\n  }\n  return self\n}\n", "export type NodeRectType = {\n  top: number\n  right: number\n  bottom: number\n  left: number\n  width: number\n  height: number\n}\n\nexport type NodeRectsType = {\n  measure: (node: HTMLElement) => NodeRectType\n}\n\nexport function NodeRects(): NodeRectsType {\n  function measure(node: HTMLElement): NodeRectType {\n    const { offsetTop, offsetLeft, offsetWidth, offsetHeight } = node\n    const offset: NodeRectType = {\n      top: offsetTop,\n      right: offsetLeft + offsetWidth,\n      bottom: offsetTop + offsetHeight,\n      left: offsetLeft,\n      width: offsetWidth,\n      height: offsetHeight\n    }\n\n    return offset\n  }\n\n  const self: NodeRectsType = {\n    measure\n  }\n  return self\n}\n", "export type PercentOfViewType = {\n  measure: (n: number) => number\n}\n\nexport function PercentOfView(viewSize: number): PercentOfViewType {\n  function measure(n: number): number {\n    return viewSize * (n / 100)\n  }\n\n  const self: PercentOfViewType = {\n    measure\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { NodeRectsType } from './NodeRects'\nimport { isBoolean, mathAbs, WindowType } from './utils'\n\ntype ResizeHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  entries: ResizeObserverEntry[]\n) => boolean | void\n\nexport type ResizeHandlerOptionType = boolean | ResizeHandlerCallbackType\n\nexport type ResizeHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n}\n\nexport function ResizeHandler(\n  container: HTMLElement,\n  eventHandler: EventHandlerType,\n  ownerWindow: WindowType,\n  slides: HTMLElement[],\n  axis: AxisType,\n  watchResize: ResizeHandlerOptionType,\n  nodeRects: NodeRectsType\n): ResizeHandlerType {\n  const observeNodes = [container].concat(slides)\n  let resizeObserver: ResizeObserver\n  let containerSize: number\n  let slideSizes: number[] = []\n  let destroyed = false\n\n  function readSize(node: HTMLElement): number {\n    return axis.measureSize(nodeRects.measure(node))\n  }\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchResize) return\n\n    containerSize = readSize(container)\n    slideSizes = slides.map(readSize)\n\n    function defaultCallback(entries: ResizeObserverEntry[]): void {\n      for (const entry of entries) {\n        if (destroyed) return\n\n        const isContainer = entry.target === container\n        const slideIndex = slides.indexOf(<HTMLElement>entry.target)\n        const lastSize = isContainer ? containerSize : slideSizes[slideIndex]\n        const newSize = readSize(isContainer ? container : slides[slideIndex])\n        const diffSize = mathAbs(newSize - lastSize)\n\n        if (diffSize >= 0.5) {\n          emblaApi.reInit()\n          eventHandler.emit('resize')\n\n          break\n        }\n      }\n    }\n\n    resizeObserver = new ResizeObserver((entries) => {\n      if (isBoolean(watchResize) || watchResize(emblaApi, entries)) {\n        defaultCallback(entries)\n      }\n    })\n\n    ownerWindow.requestAnimationFrame(() => {\n      observeNodes.forEach((node) => resizeObserver.observe(node))\n    })\n  }\n\n  function destroy(): void {\n    destroyed = true\n    if (resizeObserver) resizeObserver.disconnect()\n  }\n\n  const self: ResizeHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { mathSign, mathAbs } from './utils'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollBodyType = {\n  direction: () => number\n  duration: () => number\n  velocity: () => number\n  seek: () => ScrollBodyType\n  settled: () => boolean\n  useBaseFriction: () => ScrollBodyType\n  useBaseDuration: () => ScrollBodyType\n  useFriction: (n: number) => ScrollBodyType\n  useDuration: (n: number) => ScrollBodyType\n}\n\nexport function ScrollBody(\n  location: Vector1DType,\n  offsetLocation: Vector1DType,\n  previousLocation: Vector1DType,\n  target: Vector1DType,\n  baseDuration: number,\n  baseFriction: number\n): ScrollBodyType {\n  let scrollVelocity = 0\n  let scrollDirection = 0\n  let scrollDuration = baseDuration\n  let scrollFriction = baseFriction\n  let rawLocation = location.get()\n  let rawLocationPrevious = 0\n\n  function seek(): ScrollBodyType {\n    const displacement = target.get() - location.get()\n    const isInstant = !scrollDuration\n    let scrollDistance = 0\n\n    if (isInstant) {\n      scrollVelocity = 0\n      previousLocation.set(target)\n      location.set(target)\n\n      scrollDistance = displacement\n    } else {\n      previousLocation.set(location)\n\n      scrollVelocity += displacement / scrollDuration\n      scrollVelocity *= scrollFriction\n      rawLocation += scrollVelocity\n      location.add(scrollVelocity)\n\n      scrollDistance = rawLocation - rawLocationPrevious\n    }\n\n    scrollDirection = mathSign(scrollDistance)\n    rawLocationPrevious = rawLocation\n    return self\n  }\n\n  function settled(): boolean {\n    const diff = target.get() - offsetLocation.get()\n    return mathAbs(diff) < 0.001\n  }\n\n  function duration(): number {\n    return scrollDuration\n  }\n\n  function direction(): number {\n    return scrollDirection\n  }\n\n  function velocity(): number {\n    return scrollVelocity\n  }\n\n  function useBaseDuration(): ScrollBodyType {\n    return useDuration(baseDuration)\n  }\n\n  function useBaseFriction(): ScrollBodyType {\n    return useFriction(baseFriction)\n  }\n\n  function useDuration(n: number): ScrollBodyType {\n    scrollDuration = n\n    return self\n  }\n\n  function useFriction(n: number): ScrollBodyType {\n    scrollFriction = n\n    return self\n  }\n\n  const self: ScrollBodyType = {\n    direction,\n    duration,\n    velocity,\n    seek,\n    settled,\n    useBaseFriction,\n    useBaseDuration,\n    useFriction,\n    useDuration\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { ScrollBodyType } from './ScrollBody'\nimport { Vector1DType } from './Vector1d'\nimport { mathAbs } from './utils'\nimport { PercentOfViewType } from './PercentOfView'\n\nexport type ScrollBoundsType = {\n  shouldConstrain: () => boolean\n  constrain: (pointerDown: boolean) => void\n  toggleActive: (active: boolean) => void\n}\n\nexport function ScrollBounds(\n  limit: LimitType,\n  location: Vector1DType,\n  target: Vector1DType,\n  scrollBody: ScrollBodyType,\n  percentOfView: PercentOfViewType\n): ScrollBoundsType {\n  const pullBackThreshold = percentOfView.measure(10)\n  const edgeOffsetTolerance = percentOfView.measure(50)\n  const frictionLimit = Limit(0.1, 0.99)\n  let disabled = false\n\n  function shouldConstrain(): boolean {\n    if (disabled) return false\n    if (!limit.reachedAny(target.get())) return false\n    if (!limit.reachedAny(location.get())) return false\n    return true\n  }\n\n  function constrain(pointerDown: boolean): void {\n    if (!shouldConstrain()) return\n    const edge = limit.reachedMin(location.get()) ? 'min' : 'max'\n    const diffToEdge = mathAbs(limit[edge] - location.get())\n    const diffToTarget = target.get() - location.get()\n    const friction = frictionLimit.constrain(diffToEdge / edgeOffsetTolerance)\n\n    target.subtract(diffToTarget * friction)\n\n    if (!pointerDown && mathAbs(diffToTarget) < pullBackThreshold) {\n      target.set(limit.constrain(target.get()))\n      scrollBody.useDuration(25).useBaseFriction()\n    }\n  }\n\n  function toggleActive(active: boolean): void {\n    disabled = !active\n  }\n\n  const self: ScrollBoundsType = {\n    shouldConstrain,\n    constrain,\n    toggleActive\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { arrayIsLastIndex, arrayLast, deltaAbs } from './utils'\n\nexport type ScrollContainOptionType = false | 'trimSnaps' | 'keepSnaps'\n\nexport type ScrollContainType = {\n  snapsContained: number[]\n  scrollContainLimit: LimitType\n}\n\nexport function ScrollContain(\n  viewSize: number,\n  contentSize: number,\n  snapsAligned: number[],\n  containScroll: ScrollContainOptionType,\n  pixelTolerance: number\n): ScrollContainType {\n  const scrollBounds = Limit(-contentSize + viewSize, 0)\n  const snapsBounded = measureBounded()\n  const scrollContainLimit = findScrollContainLimit()\n  const snapsContained = measureContained()\n\n  function usePixelTolerance(bound: number, snap: number): boolean {\n    return deltaAbs(bound, snap) < 1\n  }\n\n  function findScrollContainLimit(): LimitType {\n    const startSnap = snapsBounded[0]\n    const endSnap = arrayLast(snapsBounded)\n    const min = snapsBounded.lastIndexOf(startSnap)\n    const max = snapsBounded.indexOf(endSnap) + 1\n    return Limit(min, max)\n  }\n\n  function measureBounded(): number[] {\n    return snapsAligned\n      .map((snapAligned, index) => {\n        const { min, max } = scrollBounds\n        const snap = scrollBounds.constrain(snapAligned)\n        const isFirst = !index\n        const isLast = arrayIsLastIndex(snapsAligned, index)\n        if (isFirst) return max\n        if (isLast) return min\n        if (usePixelTolerance(min, snap)) return min\n        if (usePixelTolerance(max, snap)) return max\n        return snap\n      })\n      .map((scrollBound) => parseFloat(scrollBound.toFixed(3)))\n  }\n\n  function measureContained(): number[] {\n    if (contentSize <= viewSize + pixelTolerance) return [scrollBounds.max]\n    if (containScroll === 'keepSnaps') return snapsBounded\n    const { min, max } = scrollContainLimit\n    return snapsBounded.slice(min, max)\n  }\n\n  const self: ScrollContainType = {\n    snapsContained,\n    scrollContainLimit\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { arrayLast } from './utils'\n\nexport type ScrollLimitType = {\n  limit: LimitType\n}\n\nexport function ScrollLimit(\n  contentSize: number,\n  scrollSnaps: number[],\n  loop: boolean\n): ScrollLimitType {\n  const max = scrollSnaps[0]\n  const min = loop ? max - contentSize : arrayLast(scrollSnaps)\n  const limit = Limit(min, max)\n\n  const self: ScrollLimitType = {\n    limit\n  }\n  return self\n}\n", "import { Limit, LimitType } from './Limit'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollLooperType = {\n  loop: (direction: number) => void\n}\n\nexport function ScrollLooper(\n  contentSize: number,\n  limit: LimitType,\n  location: Vector1DType,\n  vectors: Vector1DType[]\n): ScrollLooperType {\n  const jointSafety = 0.1\n  const min = limit.min + jointSafety\n  const max = limit.max + jointSafety\n  const { reachedMin, reachedMax } = Limit(min, max)\n\n  function shouldLoop(direction: number): boolean {\n    if (direction === 1) return reachedMax(location.get())\n    if (direction === -1) return reachedMin(location.get())\n    return false\n  }\n\n  function loop(direction: number): void {\n    if (!shouldLoop(direction)) return\n\n    const loopDistance = contentSize * (direction * -1)\n    vectors.forEach((v) => v.add(loopDistance))\n  }\n\n  const self: ScrollLooperType = {\n    loop\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\n\nexport type ScrollProgressType = {\n  get: (n: number) => number\n}\n\nexport function ScrollProgress(limit: LimitType): ScrollProgressType {\n  const { max, length } = limit\n\n  function get(n: number): number {\n    const currentLocation = n - max\n    return length ? currentLocation / -length : 0\n  }\n\n  const self: ScrollProgressType = {\n    get\n  }\n  return self\n}\n", "import { AlignmentType } from './Alignment'\nimport { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport { SlidesToScrollType } from './SlidesToScroll'\nimport { arrayLast, mathAbs } from './utils'\n\nexport type ScrollSnapsType = {\n  snaps: number[]\n  snapsAligned: number[]\n}\n\nexport function ScrollSnaps(\n  axis: AxisType,\n  alignment: AlignmentType,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  slidesToScroll: SlidesToScrollType\n): ScrollSnapsType {\n  const { startEdge, endEdge } = axis\n  const { groupSlides } = slidesToScroll\n  const alignments = measureSizes().map(alignment.measure)\n  const snaps = measureUnaligned()\n  const snapsAligned = measureAligned()\n\n  function measureSizes(): number[] {\n    return groupSlides(slideRects)\n      .map((rects) => arrayLast(rects)[endEdge] - rects[0][startEdge])\n      .map(mathAbs)\n  }\n\n  function measureUnaligned(): number[] {\n    return slideRects\n      .map((rect) => containerRect[startEdge] - rect[startEdge])\n      .map((snap) => -mathAbs(snap))\n  }\n\n  function measureAligned(): number[] {\n    return groupSlides(snaps)\n      .map((g) => g[0])\n      .map((snap, index) => snap + alignments[index])\n  }\n\n  const self: ScrollSnapsType = {\n    snaps,\n    snapsAligned\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\nimport { ScrollContainOptionType } from './ScrollContain'\nimport { SlidesToScrollType } from './SlidesToScroll'\nimport {\n  arrayFromNumber,\n  arrayIsLastIndex,\n  arrayLast,\n  arrayLastIndex\n} from './utils'\n\nexport type SlideRegistryType = {\n  slideRegistry: number[][]\n}\n\nexport function SlideRegistry(\n  containSnaps: boolean,\n  containScroll: ScrollContainOptionType,\n  scrollSnaps: number[],\n  scrollContainLimit: LimitType,\n  slidesToScroll: SlidesToScrollType,\n  slideIndexes: number[]\n): SlideRegistryType {\n  const { groupSlides } = slidesToScroll\n  const { min, max } = scrollContainLimit\n  const slideRegistry = createSlideRegistry()\n\n  function createSlideRegistry(): number[][] {\n    const groupedSlideIndexes = groupSlides(slideIndexes)\n    const doNotContain = !containSnaps || containScroll === 'keepSnaps'\n\n    if (scrollSnaps.length === 1) return [slideIndexes]\n    if (doNotContain) return groupedSlideIndexes\n\n    return groupedSlideIndexes.slice(min, max).map((group, index, groups) => {\n      const isFirst = !index\n      const isLast = arrayIsLastIndex(groups, index)\n\n      if (isFirst) {\n        const range = arrayLast(groups[0]) + 1\n        return arrayFromNumber(range)\n      }\n      if (isLast) {\n        const range = arrayLastIndex(slideIndexes) - arrayLast(groups)[0] + 1\n        return arrayFromNumber(range, arrayLast(groups)[0])\n      }\n      return group\n    })\n  }\n\n  const self: SlideRegistryType = {\n    slideRegistry\n  }\n  return self\n}\n", "import { LimitType } from './Limit'\nimport { Vector1DType } from './Vector1d'\nimport { arrayLast, mathAbs, mathSign } from './utils'\n\nexport type TargetType = {\n  distance: number\n  index: number\n}\n\nexport type ScrollTargetType = {\n  byIndex: (target: number, direction: number) => TargetType\n  byDistance: (force: number, snap: boolean) => TargetType\n  shortcut: (target: number, direction: number) => number\n}\n\nexport function ScrollTarget(\n  loop: boolean,\n  scrollSnaps: number[],\n  contentSize: number,\n  limit: LimitType,\n  targetVector: Vector1DType\n): ScrollTargetType {\n  const { reachedAny, removeOffset, constrain } = limit\n\n  function minDistance(distances: number[]): number {\n    return distances.concat().sort((a, b) => mathAbs(a) - mathAbs(b))[0]\n  }\n\n  function findTargetSnap(target: number): TargetType {\n    const distance = loop ? removeOffset(target) : constrain(target)\n    const ascDiffsToSnaps = scrollSnaps\n      .map((snap, index) => ({ diff: shortcut(snap - distance, 0), index }))\n      .sort((d1, d2) => mathAbs(d1.diff) - mathAbs(d2.diff))\n\n    const { index } = ascDiffsToSnaps[0]\n    return { index, distance }\n  }\n\n  function shortcut(target: number, direction: number): number {\n    const targets = [target, target + contentSize, target - contentSize]\n\n    if (!loop) return target\n    if (!direction) return minDistance(targets)\n\n    const matchingTargets = targets.filter((t) => mathSign(t) === direction)\n    if (matchingTargets.length) return minDistance(matchingTargets)\n    return arrayLast(targets) - contentSize\n  }\n\n  function byIndex(index: number, direction: number): TargetType {\n    const diffToSnap = scrollSnaps[index] - targetVector.get()\n    const distance = shortcut(diffToSnap, direction)\n    return { index, distance }\n  }\n\n  function byDistance(distance: number, snap: boolean): TargetType {\n    const target = targetVector.get() + distance\n    const { index, distance: targetSnapDistance } = findTargetSnap(target)\n    const reachedBound = !loop && reachedAny(target)\n\n    if (!snap || reachedBound) return { index, distance }\n\n    const diffToSnap = scrollSnaps[index] - targetSnapDistance\n    const snapDistance = distance + shortcut(diffToSnap, 0)\n\n    return { index, distance: snapDistance }\n  }\n\n  const self: ScrollTargetType = {\n    byDistance,\n    byIndex,\n    shortcut\n  }\n  return self\n}\n", "import { AnimationsType } from './Animations'\nimport { CounterType } from './Counter'\nimport { EventHandlerType } from './EventHandler'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollTargetType, TargetType } from './ScrollTarget'\nimport { Vector1DType } from './Vector1d'\n\nexport type ScrollToType = {\n  distance: (n: number, snap: boolean) => void\n  index: (n: number, direction: number) => void\n}\n\nexport function ScrollTo(\n  animation: AnimationsType,\n  indexCurrent: CounterType,\n  indexPrevious: CounterType,\n  scrollBody: ScrollBodyType,\n  scrollTarget: ScrollTargetType,\n  targetVector: Vector1DType,\n  eventHandler: EventHandlerType\n): ScrollToType {\n  function scrollTo(target: TargetType): void {\n    const distanceDiff = target.distance\n    const indexDiff = target.index !== indexCurrent.get()\n\n    targetVector.add(distanceDiff)\n\n    if (distanceDiff) {\n      if (scrollBody.duration()) {\n        animation.start()\n      } else {\n        animation.update()\n        animation.render(1)\n        animation.update()\n      }\n    }\n\n    if (indexDiff) {\n      indexPrevious.set(indexCurrent.get())\n      indexCurrent.set(target.index)\n      eventHandler.emit('select')\n    }\n  }\n\n  function distance(n: number, snap: boolean): void {\n    const target = scrollTarget.byDistance(n, snap)\n    scrollTo(target)\n  }\n\n  function index(n: number, direction: number): void {\n    const targetIndex = indexCurrent.clone().set(n)\n    const target = scrollTarget.byIndex(targetIndex.get(), direction)\n    scrollTo(target)\n  }\n\n  const self: ScrollToType = {\n    distance,\n    index\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { EventStoreType } from './EventStore'\nimport { ScrollBodyType } from './ScrollBody'\nimport { ScrollToType } from './ScrollTo'\nimport { SlideRegistryType } from './SlideRegistry'\nimport { isBoolean, isNumber } from './utils'\n\ntype FocusHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  evt: FocusEvent\n) => boolean | void\n\nexport type FocusHandlerOptionType = boolean | FocusHandlerCallbackType\n\nexport type SlideFocusType = {\n  init: (emblaApi: EmblaCarouselType) => void\n}\n\nexport function SlideFocus(\n  root: HTMLElement,\n  slides: HTMLElement[],\n  slideRegistry: SlideRegistryType['slideRegistry'],\n  scrollTo: ScrollToType,\n  scrollBody: ScrollBodyType,\n  eventStore: EventStoreType,\n  eventHandler: EventHandlerType,\n  watchFocus: FocusHandlerOptionType\n): SlideFocusType {\n  const focusListenerOptions = { passive: true, capture: true }\n  let lastTabPressTime = 0\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchFocus) return\n\n    function defaultCallback(index: number): void {\n      const nowTime = new Date().getTime()\n      const diffTime = nowTime - lastTabPressTime\n\n      if (diffTime > 10) return\n\n      eventHandler.emit('slideFocusStart')\n      root.scrollLeft = 0\n\n      const group = slideRegistry.findIndex((group) => group.includes(index))\n\n      if (!isNumber(group)) return\n\n      scrollBody.useDuration(0)\n      scrollTo.index(group, 0)\n\n      eventHandler.emit('slideFocus')\n    }\n\n    eventStore.add(document, 'keydown', registerTabPress, false)\n\n    slides.forEach((slide, slideIndex) => {\n      eventStore.add(\n        slide,\n        'focus',\n        (evt: FocusEvent) => {\n          if (isBoolean(watchFocus) || watchFocus(emblaApi, evt)) {\n            defaultCallback(slideIndex)\n          }\n        },\n        focusListenerOptions\n      )\n    })\n  }\n\n  function registerTabPress(event: KeyboardEvent): void {\n    if (event.code === 'Tab') lastTabPressTime = new Date().getTime()\n  }\n\n  const self: SlideFocusType = {\n    init\n  }\n  return self\n}\n", "import { isNumber } from './utils'\n\nexport type Vector1DType = {\n  get: () => number\n  set: (n: Vector1DType | number) => void\n  add: (n: Vector1DType | number) => void\n  subtract: (n: Vector1DType | number) => void\n}\n\nexport function Vector1D(initialValue: number): Vector1DType {\n  let value = initialValue\n\n  function get(): number {\n    return value\n  }\n\n  function set(n: Vector1DType | number): void {\n    value = normalizeInput(n)\n  }\n\n  function add(n: Vector1DType | number): void {\n    value += normalizeInput(n)\n  }\n\n  function subtract(n: Vector1DType | number): void {\n    value -= normalizeInput(n)\n  }\n\n  function normalizeInput(n: Vector1DType | number): number {\n    return isNumber(n) ? n : n.get()\n  }\n\n  const self: Vector1DType = {\n    get,\n    set,\n    add,\n    subtract\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { roundToTwoDecimals } from './utils'\n\nexport type TranslateType = {\n  clear: () => void\n  to: (target: number) => void\n  toggleActive: (active: boolean) => void\n}\n\nexport function Translate(\n  axis: AxisType,\n  container: HTMLElement\n): TranslateType {\n  const translate = axis.scroll === 'x' ? x : y\n  const containerStyle = container.style\n  let previousTarget: number | null = null\n  let disabled = false\n\n  function x(n: number): string {\n    return `translate3d(${n}px,0px,0px)`\n  }\n\n  function y(n: number): string {\n    return `translate3d(0px,${n}px,0px)`\n  }\n\n  function to(target: number): void {\n    if (disabled) return\n\n    const newTarget = roundToTwoDecimals(axis.direction(target))\n    if (newTarget === previousTarget) return\n\n    containerStyle.transform = translate(newTarget)\n    previousTarget = newTarget\n  }\n\n  function toggleActive(active: boolean): void {\n    disabled = !active\n  }\n\n  function clear(): void {\n    if (disabled) return\n    containerStyle.transform = ''\n    if (!container.getAttribute('style')) container.removeAttribute('style')\n  }\n\n  const self: TranslateType = {\n    clear,\n    to,\n    toggleActive\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { arrayKeys } from './utils'\nimport { Vector1D, Vector1DType } from './Vector1d'\nimport { Translate, TranslateType } from './Translate'\n\ntype SlideBoundType = {\n  start: number\n  end: number\n}\n\ntype LoopPointType = {\n  loopPoint: number\n  index: number\n  translate: TranslateType\n  slideLocation: Vector1DType\n  target: () => number\n}\n\nexport type SlideLooperType = {\n  canLoop: () => boolean\n  clear: () => void\n  loop: () => void\n  loopPoints: LoopPointType[]\n}\n\nexport function SlideLooper(\n  axis: AxisType,\n  viewSize: number,\n  contentSize: number,\n  slideSizes: number[],\n  slideSizesWithGaps: number[],\n  snaps: number[],\n  scrollSnaps: number[],\n  location: Vector1DType,\n  slides: HTMLElement[]\n): SlideLooperType {\n  const roundingSafety = 0.5\n  const ascItems = arrayKeys(slideSizesWithGaps)\n  const descItems = arrayKeys(slideSizesWithGaps).reverse()\n  const loopPoints = startPoints().concat(endPoints())\n\n  function removeSlideSizes(indexes: number[], from: number): number {\n    return indexes.reduce((a: number, i) => {\n      return a - slideSizesWithGaps[i]\n    }, from)\n  }\n\n  function slidesInGap(indexes: number[], gap: number): number[] {\n    return indexes.reduce((a: number[], i) => {\n      const remainingGap = removeSlideSizes(a, gap)\n      return remainingGap > 0 ? a.concat([i]) : a\n    }, [])\n  }\n\n  function findSlideBounds(offset: number): SlideBoundType[] {\n    return snaps.map((snap, index) => ({\n      start: snap - slideSizes[index] + roundingSafety + offset,\n      end: snap + viewSize - roundingSafety + offset\n    }))\n  }\n\n  function findLoopPoints(\n    indexes: number[],\n    offset: number,\n    isEndEdge: boolean\n  ): LoopPointType[] {\n    const slideBounds = findSlideBounds(offset)\n\n    return indexes.map((index) => {\n      const initial = isEndEdge ? 0 : -contentSize\n      const altered = isEndEdge ? contentSize : 0\n      const boundEdge = isEndEdge ? 'end' : 'start'\n      const loopPoint = slideBounds[index][boundEdge]\n\n      return {\n        index,\n        loopPoint,\n        slideLocation: Vector1D(-1),\n        translate: Translate(axis, slides[index]),\n        target: () => (location.get() > loopPoint ? initial : altered)\n      }\n    })\n  }\n\n  function startPoints(): LoopPointType[] {\n    const gap = scrollSnaps[0]\n    const indexes = slidesInGap(descItems, gap)\n    return findLoopPoints(indexes, contentSize, false)\n  }\n\n  function endPoints(): LoopPointType[] {\n    const gap = viewSize - scrollSnaps[0] - 1\n    const indexes = slidesInGap(ascItems, gap)\n    return findLoopPoints(indexes, -contentSize, true)\n  }\n\n  function canLoop(): boolean {\n    return loopPoints.every(({ index }) => {\n      const otherIndexes = ascItems.filter((i) => i !== index)\n      return removeSlideSizes(otherIndexes, viewSize) <= 0.1\n    })\n  }\n\n  function loop(): void {\n    loopPoints.forEach((loopPoint) => {\n      const { target, translate, slideLocation } = loopPoint\n      const shiftLocation = target()\n      if (shiftLocation === slideLocation.get()) return\n      translate.to(shiftLocation)\n      slideLocation.set(shiftLocation)\n    })\n  }\n\n  function clear(): void {\n    loopPoints.forEach((loopPoint) => loopPoint.translate.clear())\n  }\n\n  const self: SlideLooperType = {\n    canLoop,\n    clear,\n    loop,\n    loopPoints\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { EventHandlerType } from './EventHandler'\nimport { isBoolean } from './utils'\n\ntype SlidesHandlerCallbackType = (\n  emblaApi: EmblaCarouselType,\n  mutations: MutationRecord[]\n) => boolean | void\n\nexport type SlidesHandlerOptionType = boolean | SlidesHandlerCallbackType\n\nexport type SlidesHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  destroy: () => void\n}\n\nexport function SlidesHandler(\n  container: HTMLElement,\n  eventHandler: EventHandlerType,\n  watchSlides: SlidesHandlerOptionType\n): SlidesHandlerType {\n  let mutationObserver: MutationObserver\n  let destroyed = false\n\n  function init(emblaApi: EmblaCarouselType): void {\n    if (!watchSlides) return\n\n    function defaultCallback(mutations: MutationRecord[]): void {\n      for (const mutation of mutations) {\n        if (mutation.type === 'childList') {\n          emblaApi.reInit()\n          eventHandler.emit('slidesChanged')\n          break\n        }\n      }\n    }\n\n    mutationObserver = new MutationObserver((mutations) => {\n      if (destroyed) return\n      if (isBoolean(watchSlides) || watchSlides(emblaApi, mutations)) {\n        defaultCallback(mutations)\n      }\n    })\n\n    mutationObserver.observe(container, { childList: true })\n  }\n\n  function destroy(): void {\n    if (mutationObserver) mutationObserver.disconnect()\n    destroyed = true\n  }\n\n  const self: SlidesHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { EventHandlerType } from './EventHandler'\nimport { objectKeys } from './utils'\n\ntype IntersectionEntryMapType = {\n  [key: number]: IntersectionObserverEntry\n}\n\nexport type SlidesInViewOptionsType = IntersectionObserverInit['threshold']\n\nexport type SlidesInViewType = {\n  init: () => void\n  destroy: () => void\n  get: (inView?: boolean) => number[]\n}\n\nexport function SlidesInView(\n  container: HTMLElement,\n  slides: HTMLElement[],\n  eventHandler: EventHandlerType,\n  threshold: SlidesInViewOptionsType\n): SlidesInViewType {\n  const intersectionEntryMap: IntersectionEntryMapType = {}\n  let inViewCache: number[] | null = null\n  let notInViewCache: number[] | null = null\n  let intersectionObserver: IntersectionObserver\n  let destroyed = false\n\n  function init(): void {\n    intersectionObserver = new IntersectionObserver(\n      (entries) => {\n        if (destroyed) return\n\n        entries.forEach((entry) => {\n          const index = slides.indexOf(<HTMLElement>entry.target)\n          intersectionEntryMap[index] = entry\n        })\n\n        inViewCache = null\n        notInViewCache = null\n        eventHandler.emit('slidesInView')\n      },\n      {\n        root: container.parentElement,\n        threshold\n      }\n    )\n\n    slides.forEach((slide) => intersectionObserver.observe(slide))\n  }\n\n  function destroy(): void {\n    if (intersectionObserver) intersectionObserver.disconnect()\n    destroyed = true\n  }\n\n  function createInViewList(inView: boolean): number[] {\n    return objectKeys(intersectionEntryMap).reduce(\n      (list: number[], slideIndex) => {\n        const index = parseInt(slideIndex)\n        const { isIntersecting } = intersectionEntryMap[index]\n        const inViewMatch = inView && isIntersecting\n        const notInViewMatch = !inView && !isIntersecting\n\n        if (inViewMatch || notInViewMatch) list.push(index)\n        return list\n      },\n      []\n    )\n  }\n\n  function get(inView: boolean = true): number[] {\n    if (inView && inViewCache) return inViewCache\n    if (!inView && notInViewCache) return notInViewCache\n\n    const slideIndexes = createInViewList(inView)\n\n    if (inView) inViewCache = slideIndexes\n    if (!inView) notInViewCache = slideIndexes\n\n    return slideIndexes\n  }\n\n  const self: SlidesInViewType = {\n    init,\n    destroy,\n    get\n  }\n\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport { arrayIsLastIndex, arrayLast, mathAbs, WindowType } from './utils'\n\nexport type SlideSizesType = {\n  slideSizes: number[]\n  slideSizesWithGaps: number[]\n  startGap: number\n  endGap: number\n}\n\nexport function SlideSizes(\n  axis: AxisType,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  slides: HTMLElement[],\n  readEdgeGap: boolean,\n  ownerWindow: WindowType\n): SlideSizesType {\n  const { measureSize, startEdge, endEdge } = axis\n  const withEdgeGap = slideRects[0] && readEdgeGap\n  const startGap = measureStartGap()\n  const endGap = measureEndGap()\n  const slideSizes = slideRects.map(measureSize)\n  const slideSizesWithGaps = measureWithGaps()\n\n  function measureStartGap(): number {\n    if (!withEdgeGap) return 0\n    const slideRect = slideRects[0]\n    return mathAbs(containerRect[startEdge] - slideRect[startEdge])\n  }\n\n  function measureEndGap(): number {\n    if (!withEdgeGap) return 0\n    const style = ownerWindow.getComputedStyle(arrayLast(slides))\n    return parseFloat(style.getPropertyValue(`margin-${endEdge}`))\n  }\n\n  function measureWithGaps(): number[] {\n    return slideRects\n      .map((rect, index, rects) => {\n        const isFirst = !index\n        const isLast = arrayIsLastIndex(rects, index)\n        if (isFirst) return slideSizes[index] + startGap\n        if (isLast) return slideSizes[index] + endGap\n        return rects[index + 1][startEdge] - rect[startEdge]\n      })\n      .map(mathAbs)\n  }\n\n  const self: SlideSizesType = {\n    slideSizes,\n    slideSizesWithGaps,\n    startGap,\n    endGap\n  }\n  return self\n}\n", "import { AxisType } from './Axis'\nimport { NodeRectType } from './NodeRects'\nimport {\n  arrayKeys,\n  arrayLast,\n  arrayLastIndex,\n  isNumber,\n  mathAbs\n} from './utils'\n\nexport type SlidesToScrollOptionType = 'auto' | number\n\nexport type SlidesToScrollType = {\n  groupSlides: <Type>(array: Type[]) => Type[][]\n}\n\nexport function SlidesToScroll(\n  axis: AxisType,\n  viewSize: number,\n  slidesToScroll: SlidesToScrollOptionType,\n  loop: boolean,\n  containerRect: NodeRectType,\n  slideRects: NodeRectType[],\n  startGap: number,\n  endGap: number,\n  pixelTolerance: number\n): SlidesToScrollType {\n  const { startEdge, endEdge, direction } = axis\n  const groupByNumber = isNumber(slidesToScroll)\n\n  function byNumber<Type>(array: Type[], groupSize: number): Type[][] {\n    return arrayKeys(array)\n      .filter((i) => i % groupSize === 0)\n      .map((i) => array.slice(i, i + groupSize))\n  }\n\n  function bySize<Type>(array: Type[]): Type[][] {\n    if (!array.length) return []\n\n    return arrayKeys(array)\n      .reduce((groups: number[], rectB, index) => {\n        const rectA = arrayLast(groups) || 0\n        const isFirst = rectA === 0\n        const isLast = rectB === arrayLastIndex(array)\n\n        const edgeA = containerRect[startEdge] - slideRects[rectA][startEdge]\n        const edgeB = containerRect[startEdge] - slideRects[rectB][endEdge]\n        const gapA = !loop && isFirst ? direction(startGap) : 0\n        const gapB = !loop && isLast ? direction(endGap) : 0\n        const chunkSize = mathAbs(edgeB - gapB - (edgeA + gapA))\n\n        if (index && chunkSize > viewSize + pixelTolerance) groups.push(rectB)\n        if (isLast) groups.push(array.length)\n        return groups\n      }, [])\n      .map((currentSize, index, groups) => {\n        const previousSize = Math.max(groups[index - 1] || 0)\n        return array.slice(previousSize, currentSize)\n      })\n  }\n\n  function groupSlides<Type>(array: Type[]): Type[][] {\n    return groupByNumber ? byNumber(array, slidesToScroll) : bySize(array)\n  }\n\n  const self: SlidesToScrollType = {\n    groupSlides\n  }\n  return self\n}\n", "import { Alignment } from './Alignment'\nimport {\n  Animations,\n  AnimationsType,\n  AnimationsUpdateType,\n  AnimationsRenderType\n} from './Animations'\nimport { Axis, AxisType } from './Axis'\nimport { Counter, CounterType } from './Counter'\nimport { DragHandler, DragHandlerType } from './DragHandler'\nimport { DragTracker } from './DragTracker'\nimport { EventHandlerType } from './EventHandler'\nimport { EventStore, EventStoreType } from './EventStore'\nimport { LimitType } from './Limit'\nimport { NodeRectType, NodeRects } from './NodeRects'\nimport { OptionsType } from './Options'\nimport { PercentOfView, PercentOfViewType } from './PercentOfView'\nimport { ResizeHandler, ResizeHandlerType } from './ResizeHandler'\nimport { ScrollBody, ScrollBodyType } from './ScrollBody'\nimport { ScrollBounds, ScrollBoundsType } from './ScrollBounds'\nimport { ScrollContain } from './ScrollContain'\nimport { ScrollLimit } from './ScrollLimit'\nimport { Sc<PERSON>Looper, ScrollLooperType } from './ScrollLooper'\nimport { ScrollProgress, ScrollProgressType } from './ScrollProgress'\nimport { ScrollSnaps } from './ScrollSnaps'\nimport { SlideRegistry, SlideRegistryType } from './SlideRegistry'\nimport { ScrollTarget, ScrollTargetType } from './ScrollTarget'\nimport { ScrollTo, ScrollToType } from './ScrollTo'\nimport { SlideFocus, SlideFocusType } from './SlideFocus'\nimport { SlideLooper, SlideLooperType } from './SlideLooper'\nimport { SlidesHandler, SlidesHandlerType } from './SlidesHandler'\nimport { SlidesInView, SlidesInViewType } from './SlidesInView'\nimport { SlideSizes } from './SlideSizes'\nimport { SlidesToScroll, SlidesToScrollType } from './SlidesToScroll'\nimport { Translate, TranslateType } from './Translate'\nimport { arrayKeys, arrayLast, arrayLastIndex, WindowType } from './utils'\nimport { Vector1D, Vector1DType } from './Vector1d'\n\nexport type EngineType = {\n  ownerDocument: Document\n  ownerWindow: WindowType\n  eventHandler: EventHandlerType\n  axis: AxisType\n  animation: AnimationsType\n  scrollBounds: ScrollBoundsType\n  scrollLooper: ScrollLooperType\n  scrollProgress: ScrollProgressType\n  index: CounterType\n  indexPrevious: CounterType\n  limit: LimitType\n  location: Vector1DType\n  offsetLocation: Vector1DType\n  previousLocation: Vector1DType\n  options: OptionsType\n  percentOfView: PercentOfViewType\n  scrollBody: ScrollBodyType\n  dragHandler: DragHandlerType\n  eventStore: EventStoreType\n  slideLooper: SlideLooperType\n  slidesInView: SlidesInViewType\n  slidesToScroll: SlidesToScrollType\n  target: Vector1DType\n  translate: TranslateType\n  resizeHandler: ResizeHandlerType\n  slidesHandler: SlidesHandlerType\n  scrollTo: ScrollToType\n  scrollTarget: ScrollTargetType\n  scrollSnapList: number[]\n  scrollSnaps: number[]\n  slideIndexes: number[]\n  slideFocus: SlideFocusType\n  slideRegistry: SlideRegistryType['slideRegistry']\n  containerRect: NodeRectType\n  slideRects: NodeRectType[]\n}\n\nexport function Engine(\n  root: HTMLElement,\n  container: HTMLElement,\n  slides: HTMLElement[],\n  ownerDocument: Document,\n  ownerWindow: WindowType,\n  options: OptionsType,\n  eventHandler: EventHandlerType\n): EngineType {\n  // Options\n  const {\n    align,\n    axis: scrollAxis,\n    direction,\n    startIndex,\n    loop,\n    duration,\n    dragFree,\n    dragThreshold,\n    inViewThreshold,\n    slidesToScroll: groupSlides,\n    skipSnaps,\n    containScroll,\n    watchResize,\n    watchSlides,\n    watchDrag,\n    watchFocus\n  } = options\n\n  // Measurements\n  const pixelTolerance = 2\n  const nodeRects = NodeRects()\n  const containerRect = nodeRects.measure(container)\n  const slideRects = slides.map(nodeRects.measure)\n  const axis = Axis(scrollAxis, direction)\n  const viewSize = axis.measureSize(containerRect)\n  const percentOfView = PercentOfView(viewSize)\n  const alignment = Alignment(align, viewSize)\n  const containSnaps = !loop && !!containScroll\n  const readEdgeGap = loop || !!containScroll\n  const { slideSizes, slideSizesWithGaps, startGap, endGap } = SlideSizes(\n    axis,\n    containerRect,\n    slideRects,\n    slides,\n    readEdgeGap,\n    ownerWindow\n  )\n  const slidesToScroll = SlidesToScroll(\n    axis,\n    viewSize,\n    groupSlides,\n    loop,\n    containerRect,\n    slideRects,\n    startGap,\n    endGap,\n    pixelTolerance\n  )\n  const { snaps, snapsAligned } = ScrollSnaps(\n    axis,\n    alignment,\n    containerRect,\n    slideRects,\n    slidesToScroll\n  )\n  const contentSize = -arrayLast(snaps) + arrayLast(slideSizesWithGaps)\n  const { snapsContained, scrollContainLimit } = ScrollContain(\n    viewSize,\n    contentSize,\n    snapsAligned,\n    containScroll,\n    pixelTolerance\n  )\n  const scrollSnaps = containSnaps ? snapsContained : snapsAligned\n  const { limit } = ScrollLimit(contentSize, scrollSnaps, loop)\n\n  // Indexes\n  const index = Counter(arrayLastIndex(scrollSnaps), startIndex, loop)\n  const indexPrevious = index.clone()\n  const slideIndexes = arrayKeys(slides)\n\n  // Animation\n  const update: AnimationsUpdateType = ({\n    dragHandler,\n    scrollBody,\n    scrollBounds,\n    options: { loop }\n  }) => {\n    if (!loop) scrollBounds.constrain(dragHandler.pointerDown())\n    scrollBody.seek()\n  }\n\n  const render: AnimationsRenderType = (\n    {\n      scrollBody,\n      translate,\n      location,\n      offsetLocation,\n      previousLocation,\n      scrollLooper,\n      slideLooper,\n      dragHandler,\n      animation,\n      eventHandler,\n      scrollBounds,\n      options: { loop }\n    },\n    alpha\n  ) => {\n    const shouldSettle = scrollBody.settled()\n    const withinBounds = !scrollBounds.shouldConstrain()\n    const hasSettled = loop ? shouldSettle : shouldSettle && withinBounds\n\n    if (hasSettled && !dragHandler.pointerDown()) {\n      animation.stop()\n      eventHandler.emit('settle')\n    }\n    if (!hasSettled) eventHandler.emit('scroll')\n\n    const interpolatedLocation =\n      location.get() * alpha + previousLocation.get() * (1 - alpha)\n\n    offsetLocation.set(interpolatedLocation)\n\n    if (loop) {\n      scrollLooper.loop(scrollBody.direction())\n      slideLooper.loop()\n    }\n\n    translate.to(offsetLocation.get())\n  }\n\n  const animation = Animations(\n    ownerDocument,\n    ownerWindow,\n    () => update(engine),\n    (alpha: number) => render(engine, alpha)\n  )\n\n  // Shared\n  const friction = 0.68\n  const startLocation = scrollSnaps[index.get()]\n  const location = Vector1D(startLocation)\n  const previousLocation = Vector1D(startLocation)\n  const offsetLocation = Vector1D(startLocation)\n  const target = Vector1D(startLocation)\n  const scrollBody = ScrollBody(\n    location,\n    offsetLocation,\n    previousLocation,\n    target,\n    duration,\n    friction\n  )\n  const scrollTarget = ScrollTarget(\n    loop,\n    scrollSnaps,\n    contentSize,\n    limit,\n    target\n  )\n  const scrollTo = ScrollTo(\n    animation,\n    index,\n    indexPrevious,\n    scrollBody,\n    scrollTarget,\n    target,\n    eventHandler\n  )\n  const scrollProgress = ScrollProgress(limit)\n  const eventStore = EventStore()\n  const slidesInView = SlidesInView(\n    container,\n    slides,\n    eventHandler,\n    inViewThreshold\n  )\n  const { slideRegistry } = SlideRegistry(\n    containSnaps,\n    containScroll,\n    scrollSnaps,\n    scrollContainLimit,\n    slidesToScroll,\n    slideIndexes\n  )\n  const slideFocus = SlideFocus(\n    root,\n    slides,\n    slideRegistry,\n    scrollTo,\n    scrollBody,\n    eventStore,\n    eventHandler,\n    watchFocus\n  )\n\n  // Engine\n  const engine: EngineType = {\n    ownerDocument,\n    ownerWindow,\n    eventHandler,\n    containerRect,\n    slideRects,\n    animation,\n    axis,\n    dragHandler: DragHandler(\n      axis,\n      root,\n      ownerDocument,\n      ownerWindow,\n      target,\n      DragTracker(axis, ownerWindow),\n      location,\n      animation,\n      scrollTo,\n      scrollBody,\n      scrollTarget,\n      index,\n      eventHandler,\n      percentOfView,\n      dragFree,\n      dragThreshold,\n      skipSnaps,\n      friction,\n      watchDrag\n    ),\n    eventStore,\n    percentOfView,\n    index,\n    indexPrevious,\n    limit,\n    location,\n    offsetLocation,\n    previousLocation,\n    options,\n    resizeHandler: ResizeHandler(\n      container,\n      eventHandler,\n      ownerWindow,\n      slides,\n      axis,\n      watchResize,\n      nodeRects\n    ),\n    scrollBody,\n    scrollBounds: ScrollBounds(\n      limit,\n      offsetLocation,\n      target,\n      scrollBody,\n      percentOfView\n    ),\n    scrollLooper: ScrollLooper(contentSize, limit, offsetLocation, [\n      location,\n      offsetLocation,\n      previousLocation,\n      target\n    ]),\n    scrollProgress,\n    scrollSnapList: scrollSnaps.map(scrollProgress.get),\n    scrollSnaps,\n    scrollTarget,\n    scrollTo,\n    slideLooper: SlideLooper(\n      axis,\n      viewSize,\n      contentSize,\n      slideSizes,\n      slideSizesWithGaps,\n      snaps,\n      scrollSnaps,\n      offsetLocation,\n      slides\n    ),\n    slideFocus,\n    slidesHandler: SlidesHandler(container, eventHandler, watchSlides),\n    slidesInView,\n    slideIndexes,\n    slideRegistry,\n    slidesToScroll,\n    target,\n    translate: Translate(axis, container)\n  }\n\n  return engine\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\n\ntype CallbackType = (emblaApi: EmblaCarouselType, evt: EmblaEventType) => void\ntype ListenersType = Partial<{ [key in EmblaEventType]: CallbackType[] }>\n\nexport type EmblaEventType = EmblaEventListType[keyof EmblaEventListType]\n\nexport interface EmblaEventListType {\n  init: 'init'\n  pointerDown: 'pointerDown'\n  pointerUp: 'pointerUp'\n  slidesChanged: 'slidesChanged'\n  slidesInView: 'slidesInView'\n  scroll: 'scroll'\n  select: 'select'\n  settle: 'settle'\n  destroy: 'destroy'\n  reInit: 'reInit'\n  resize: 'resize'\n  slideFocusStart: 'slideFocusStart'\n  slideFocus: 'slideFocus'\n}\n\nexport type EventHandlerType = {\n  init: (emblaApi: EmblaCarouselType) => void\n  emit: (evt: EmblaEventType) => EventHandlerType\n  on: (evt: EmblaEventType, cb: CallbackType) => EventHandlerType\n  off: (evt: EmblaEventType, cb: CallbackType) => EventHandlerType\n  clear: () => void\n}\n\nexport function EventHandler(): EventHandlerType {\n  let listeners: ListenersType = {}\n  let api: EmblaCarouselType\n\n  function init(emblaApi: EmblaCarouselType): void {\n    api = emblaApi\n  }\n\n  function getListeners(evt: EmblaEventType): CallbackType[] {\n    return listeners[evt] || []\n  }\n\n  function emit(evt: EmblaEventType): EventHandlerType {\n    getListeners(evt).forEach((e) => e(api, evt))\n    return self\n  }\n\n  function on(evt: EmblaEventType, cb: CallbackType): EventHandlerType {\n    listeners[evt] = getListeners(evt).concat([cb])\n    return self\n  }\n\n  function off(evt: EmblaEventType, cb: CallbackType): EventHandlerType {\n    listeners[evt] = getListeners(evt).filter((e) => e !== cb)\n    return self\n  }\n\n  function clear(): void {\n    listeners = {}\n  }\n\n  const self: EventHandlerType = {\n    init,\n    emit,\n    off,\n    on,\n    clear\n  }\n  return self\n}\n", "import { AlignmentOptionType } from './Alignment'\nimport { AxisDirectionOptionType, AxisOptionType } from './Axis'\nimport { SlidesToScrollOptionType } from './SlidesToScroll'\nimport { ScrollContainOptionType } from './ScrollContain'\nimport { DragHandlerOptionType } from './DragHandler'\nimport { ResizeHandlerOptionType } from './ResizeHandler'\nimport { SlidesHandlerOptionType } from './SlidesHandler'\nimport { SlidesInViewOptionsType } from './SlidesInView'\nimport { FocusHandlerOptionType } from './SlideFocus'\n\nexport type LooseOptionsType = {\n  [key: string]: unknown\n}\n\nexport type CreateOptionsType<Type extends LooseOptionsType> = Type & {\n  active: boolean\n  breakpoints: {\n    [key: string]: Omit<Partial<CreateOptionsType<Type>>, 'breakpoints'>\n  }\n}\n\nexport type OptionsType = CreateOptionsType<{\n  align: AlignmentOptionType\n  axis: AxisOptionType\n  container: string | HTMLElement | null\n  slides: string | HTMLElement[] | NodeListOf<HTMLElement> | null\n  containScroll: ScrollContainOptionType\n  direction: AxisDirectionOptionType\n  slidesToScroll: SlidesToScrollOptionType\n  dragFree: boolean\n  dragThreshold: number\n  inViewThreshold: SlidesInViewOptionsType\n  loop: boolean\n  skipSnaps: boolean\n  duration: number\n  startIndex: number\n  watchDrag: DragHandlerOptionType\n  watchResize: ResizeHandlerOptionType\n  watchSlides: SlidesHandlerOptionType\n  watchFocus: FocusHandlerOptionType\n}>\n\nexport const defaultOptions: OptionsType = {\n  align: 'center',\n  axis: 'x',\n  container: null,\n  slides: null,\n  containScroll: 'trimSnaps',\n  direction: 'ltr',\n  slidesToScroll: 1,\n  inViewThreshold: 0,\n  breakpoints: {},\n  dragFree: false,\n  dragThreshold: 10,\n  loop: false,\n  skipSnaps: false,\n  duration: 25,\n  startIndex: 0,\n  active: true,\n  watchDrag: true,\n  watchResize: true,\n  watchSlides: true,\n  watchFocus: true\n}\n\nexport type EmblaOptionsType = Partial<OptionsType>\n", "import { LooseOptionsType, CreateOptionsType } from './Options'\nimport { objectKeys, objectsMergeDeep, WindowType } from './utils'\n\ntype OptionsType = Partial<CreateOptionsType<LooseOptionsType>>\n\nexport type OptionsHandlerType = {\n  mergeOptions: <TypeA extends OptionsType, TypeB extends OptionsType>(\n    optionsA: TypeA,\n    optionsB?: TypeB\n  ) => TypeA\n  optionsAtMedia: <Type extends OptionsType>(options: Type) => Type\n  optionsMediaQueries: (optionsList: OptionsType[]) => MediaQueryList[]\n}\n\nexport function OptionsHandler(ownerWindow: WindowType): OptionsHandlerType {\n  function mergeOptions<TypeA extends OptionsType, TypeB extends OptionsType>(\n    optionsA: TypeA,\n    optionsB?: TypeB\n  ): TypeA {\n    return <TypeA>objectsMergeDeep(optionsA, optionsB || {})\n  }\n\n  function optionsAtMedia<Type extends OptionsType>(options: Type): Type {\n    const optionsAtMedia = options.breakpoints || {}\n    const matchedMediaOptions = objectKeys(optionsAtMedia)\n      .filter((media) => ownerWindow.matchMedia(media).matches)\n      .map((media) => optionsAtMedia[media])\n      .reduce((a, mediaOption) => mergeOptions(a, mediaOption), {})\n\n    return mergeOptions(options, matchedMediaOptions)\n  }\n\n  function optionsMediaQueries(optionsList: OptionsType[]): MediaQueryList[] {\n    return optionsList\n      .map((options) => objectKeys(options.breakpoints || {}))\n      .reduce((acc, mediaQueries) => acc.concat(mediaQueries), [])\n      .map(ownerWindow.matchMedia)\n  }\n\n  const self: OptionsHandlerType = {\n    mergeOptions,\n    optionsAtMedia,\n    optionsMediaQueries\n  }\n  return self\n}\n", "import { EmblaCarouselType } from './EmblaCarousel'\nimport { OptionsHandlerType } from './OptionsHandler'\nimport { EmblaPluginsType, EmblaPluginType } from './Plugins'\n\nexport type PluginsHandlerType = {\n  init: (\n    emblaApi: EmblaCarouselType,\n    plugins: EmblaPluginType[]\n  ) => EmblaPluginsType\n  destroy: () => void\n}\n\nexport function PluginsHandler(\n  optionsHandler: OptionsHandlerType\n): PluginsHandlerType {\n  let activePlugins: EmblaPluginType[] = []\n\n  function init(\n    emblaApi: EmblaCarouselType,\n    plugins: EmblaPluginType[]\n  ): EmblaPluginsType {\n    activePlugins = plugins.filter(\n      ({ options }) => optionsHandler.optionsAtMedia(options).active !== false\n    )\n    activePlugins.forEach((plugin) => plugin.init(emblaApi, optionsHandler))\n\n    return plugins.reduce(\n      (map, plugin) => Object.assign(map, { [plugin.name]: plugin }),\n      {}\n    )\n  }\n\n  function destroy(): void {\n    activePlugins = activePlugins.filter((plugin) => plugin.destroy())\n  }\n\n  const self: PluginsHandlerType = {\n    init,\n    destroy\n  }\n  return self\n}\n", "import { Engine, EngineType } from './Engine'\nimport { EventStore } from './EventStore'\nimport { EventHandler, EventHandlerType } from './EventHandler'\nimport { defaultOptions, EmblaOptionsType, OptionsType } from './Options'\nimport { OptionsHandler } from './OptionsHandler'\nimport { PluginsHandler } from './PluginsHandler'\nimport { EmblaPluginsType, EmblaPluginType } from './Plugins'\nimport { isString, WindowType } from './utils'\n\nexport type EmblaCarouselType = {\n  canScrollNext: () => boolean\n  canScrollPrev: () => boolean\n  containerNode: () => HTMLElement\n  internalEngine: () => EngineType\n  destroy: () => void\n  off: EventHandlerType['off']\n  on: EventHandlerType['on']\n  emit: EventHandlerType['emit']\n  plugins: () => EmblaPluginsType\n  previousScrollSnap: () => number\n  reInit: (options?: EmblaOptionsType, plugins?: EmblaPluginType[]) => void\n  rootNode: () => HTMLElement\n  scrollNext: (jump?: boolean) => void\n  scrollPrev: (jump?: boolean) => void\n  scrollProgress: () => number\n  scrollSnapList: () => number[]\n  scrollTo: (index: number, jump?: boolean) => void\n  selectedScrollSnap: () => number\n  slideNodes: () => HTMLElement[]\n  slidesInView: () => number[]\n  slidesNotInView: () => number[]\n}\n\nfunction EmblaCarousel(\n  root: HTMLElement,\n  userOptions?: EmblaOptionsType,\n  userPlugins?: EmblaPluginType[]\n): EmblaCarouselType {\n  const ownerDocument = root.ownerDocument\n  const ownerWindow = <WindowType>ownerDocument.defaultView\n  const optionsHandler = OptionsHandler(ownerWindow)\n  const pluginsHandler = PluginsHandler(optionsHandler)\n  const mediaHandlers = EventStore()\n  const eventHandler = EventHandler()\n  const { mergeOptions, optionsAtMedia, optionsMediaQueries } = optionsHandler\n  const { on, off, emit } = eventHandler\n  const reInit = reActivate\n\n  let destroyed = false\n  let engine: EngineType\n  let optionsBase = mergeOptions(defaultOptions, EmblaCarousel.globalOptions)\n  let options = mergeOptions(optionsBase)\n  let pluginList: EmblaPluginType[] = []\n  let pluginApis: EmblaPluginsType\n\n  let container: HTMLElement\n  let slides: HTMLElement[]\n\n  function storeElements(): void {\n    const { container: userContainer, slides: userSlides } = options\n\n    const customContainer = isString(userContainer)\n      ? root.querySelector(userContainer)\n      : userContainer\n    container = <HTMLElement>(customContainer || root.children[0])\n\n    const customSlides = isString(userSlides)\n      ? container.querySelectorAll(userSlides)\n      : userSlides\n    slides = <HTMLElement[]>[].slice.call(customSlides || container.children)\n  }\n\n  function createEngine(options: OptionsType): EngineType {\n    const engine = Engine(\n      root,\n      container,\n      slides,\n      ownerDocument,\n      ownerWindow,\n      options,\n      eventHandler\n    )\n\n    if (options.loop && !engine.slideLooper.canLoop()) {\n      const optionsWithoutLoop = Object.assign({}, options, { loop: false })\n      return createEngine(optionsWithoutLoop)\n    }\n    return engine\n  }\n\n  function activate(\n    withOptions?: EmblaOptionsType,\n    withPlugins?: EmblaPluginType[]\n  ): void {\n    if (destroyed) return\n\n    optionsBase = mergeOptions(optionsBase, withOptions)\n    options = optionsAtMedia(optionsBase)\n    pluginList = withPlugins || pluginList\n\n    storeElements()\n\n    engine = createEngine(options)\n\n    optionsMediaQueries([\n      optionsBase,\n      ...pluginList.map(({ options }) => options)\n    ]).forEach((query) => mediaHandlers.add(query, 'change', reActivate))\n\n    if (!options.active) return\n\n    engine.translate.to(engine.location.get())\n    engine.animation.init()\n    engine.slidesInView.init()\n    engine.slideFocus.init(self)\n    engine.eventHandler.init(self)\n    engine.resizeHandler.init(self)\n    engine.slidesHandler.init(self)\n\n    if (engine.options.loop) engine.slideLooper.loop()\n    if (container.offsetParent && slides.length) engine.dragHandler.init(self)\n\n    pluginApis = pluginsHandler.init(self, pluginList)\n  }\n\n  function reActivate(\n    withOptions?: EmblaOptionsType,\n    withPlugins?: EmblaPluginType[]\n  ): void {\n    const startIndex = selectedScrollSnap()\n    deActivate()\n    activate(mergeOptions({ startIndex }, withOptions), withPlugins)\n    eventHandler.emit('reInit')\n  }\n\n  function deActivate(): void {\n    engine.dragHandler.destroy()\n    engine.eventStore.clear()\n    engine.translate.clear()\n    engine.slideLooper.clear()\n    engine.resizeHandler.destroy()\n    engine.slidesHandler.destroy()\n    engine.slidesInView.destroy()\n    engine.animation.destroy()\n    pluginsHandler.destroy()\n    mediaHandlers.clear()\n  }\n\n  function destroy(): void {\n    if (destroyed) return\n    destroyed = true\n    mediaHandlers.clear()\n    deActivate()\n    eventHandler.emit('destroy')\n    eventHandler.clear()\n  }\n\n  function scrollTo(index: number, jump?: boolean, direction?: number): void {\n    if (!options.active || destroyed) return\n    engine.scrollBody\n      .useBaseFriction()\n      .useDuration(jump === true ? 0 : options.duration)\n    engine.scrollTo.index(index, direction || 0)\n  }\n\n  function scrollNext(jump?: boolean): void {\n    const next = engine.index.add(1).get()\n    scrollTo(next, jump, -1)\n  }\n\n  function scrollPrev(jump?: boolean): void {\n    const prev = engine.index.add(-1).get()\n    scrollTo(prev, jump, 1)\n  }\n\n  function canScrollNext(): boolean {\n    const next = engine.index.add(1).get()\n    return next !== selectedScrollSnap()\n  }\n\n  function canScrollPrev(): boolean {\n    const prev = engine.index.add(-1).get()\n    return prev !== selectedScrollSnap()\n  }\n\n  function scrollSnapList(): number[] {\n    return engine.scrollSnapList\n  }\n\n  function scrollProgress(): number {\n    return engine.scrollProgress.get(engine.location.get())\n  }\n\n  function selectedScrollSnap(): number {\n    return engine.index.get()\n  }\n\n  function previousScrollSnap(): number {\n    return engine.indexPrevious.get()\n  }\n\n  function slidesInView(): number[] {\n    return engine.slidesInView.get()\n  }\n\n  function slidesNotInView(): number[] {\n    return engine.slidesInView.get(false)\n  }\n\n  function plugins(): EmblaPluginsType {\n    return pluginApis\n  }\n\n  function internalEngine(): EngineType {\n    return engine\n  }\n\n  function rootNode(): HTMLElement {\n    return root\n  }\n\n  function containerNode(): HTMLElement {\n    return container\n  }\n\n  function slideNodes(): HTMLElement[] {\n    return slides\n  }\n\n  const self: EmblaCarouselType = {\n    canScrollNext,\n    canScrollPrev,\n    containerNode,\n    internalEngine,\n    destroy,\n    off,\n    on,\n    emit,\n    plugins,\n    previousScrollSnap,\n    reInit,\n    rootNode,\n    scrollNext,\n    scrollPrev,\n    scrollProgress,\n    scrollSnapList,\n    scrollTo,\n    selectedScrollSnap,\n    slideNodes,\n    slidesInView,\n    slidesNotInView\n  }\n\n  activate(userOptions, userPlugins)\n  setTimeout(() => eventHandler.emit('init'), 0)\n  return self\n}\n\ndeclare namespace EmblaCarousel {\n  let globalOptions: EmblaOptionsType | undefined\n}\n\nEmblaCarousel.globalOptions = undefined\n\nexport default EmblaCarousel\n"], "names": ["isNumber", "subject", "isString", "isBoolean", "isObject", "Object", "prototype", "toString", "call", "mathAbs", "n", "Math", "abs", "mathSign", "sign", "deltaAbs", "valueB", "valueA", "factorAbs", "diff", "roundToTwoDecimals", "num", "round", "arrayKeys", "array", "objectKeys", "map", "Number", "arrayLast", "arrayLastIndex", "max", "length", "arrayIsLastIndex", "index", "arrayFromNumber", "startAt", "Array", "from", "_", "i", "object", "keys", "objectsMergeDeep", "objectA", "objectB", "reduce", "mergedObjects", "currentObject", "for<PERSON>ach", "key", "areObjects", "isMouseEvent", "evt", "ownerWindow", "MouseEvent", "Alignment", "align", "viewSize", "predefined", "start", "center", "end", "measure", "self", "EventStore", "listeners", "add", "node", "type", "handler", "options", "passive", "removeListener", "addEventListener", "removeEventListener", "legacyMediaQueryList", "addListener", "push", "clear", "filter", "remove", "Animations", "ownerDocument", "update", "render", "documentVisibleHandler", "fixedTimeStep", "lastTimeStamp", "accumulatedTime", "animationId", "init", "hidden", "reset", "destroy", "stop", "animate", "timeStamp", "timeElapsed", "alpha", "requestAnimationFrame", "cancelAnimationFrame", "Axis", "axis", "contentDirection", "isRightToLeft", "isVertical", "scroll", "cross", "startEdge", "getStartEdge", "endEdge", "getEndEdge", "measureSize", "nodeRect", "height", "width", "direction", "Limit", "min", "reachedMin", "reachedMax", "reachedAny", "constrain", "removeOffset", "ceil", "Counter", "loop", "loopEnd", "counter", "withinLimit", "get", "set", "clone", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "rootNode", "target", "dragTracker", "location", "animation", "scrollTo", "scrollBody", "scrollTarget", "<PERSON><PERSON><PERSON><PERSON>", "percentOfView", "dragFree", "drag<PERSON><PERSON><PERSON><PERSON>", "skipSnaps", "baseFriction", "watchDrag", "crossAxis", "focusNodes", "nonPassiveEvent", "initEvents", "dragEvents", "goToNextThreshold", "snapForceBoost", "mouse", "touch", "freeForceBoost", "baseSpeed", "isMoving", "startScroll", "startCross", "pointerIsDown", "preventScroll", "preventClick", "isMouse", "emblaApi", "downIfAllowed", "down", "preventDefault", "undefined", "up", "click", "addDragEvents", "move", "isFocusNode", "nodeName", "includes", "forceBoost", "boost", "<PERSON><PERSON><PERSON><PERSON>", "force", "targetChanged", "next", "baseForce", "byDistance", "distance", "byIndex", "isMouseEvt", "buttons", "button", "pointerDown", "useFriction", "useDuration", "readPoint", "emit", "isTouchEvt", "touches", "lastScroll", "lastCross", "diffScroll", "diffCross", "cancelable", "pointer<PERSON><PERSON>", "currentLocation", "rawForce", "pointerUp", "forceFactor", "speed", "friction", "stopPropagation", "DragTracker", "logInterval", "startEvent", "lastEvent", "readTime", "evtAxis", "property", "coord", "expired", "diffDrag", "diffTime", "isFlick", "NodeRects", "offsetTop", "offsetLeft", "offsetWidth", "offsetHeight", "offset", "top", "right", "bottom", "left", "PercentOfView", "ResizeHandler", "container", "slides", "watchResize", "nodeRects", "observeNodes", "concat", "resizeObserver", "containerSize", "slideSizes", "destroyed", "readSize", "defaultCallback", "entries", "entry", "<PERSON><PERSON><PERSON><PERSON>", "slideIndex", "indexOf", "lastSize", "newSize", "diffSize", "reInit", "ResizeObserver", "observe", "disconnect", "ScrollBody", "offsetLocation", "previousLocation", "baseDuration", "scrollVelocity", "scrollDirection", "scrollDuration", "scrollFriction", "rawLocation", "rawLocationPrevious", "seek", "displacement", "isInstant", "scrollDistance", "settled", "duration", "velocity", "useBaseDuration", "useBaseFriction", "ScrollBounds", "limit", "pullBackThreshold", "edgeOffsetTolerance", "frictionLimit", "disabled", "shouldConstrain", "edge", "diffToEdge", "diffTo<PERSON>arget", "subtract", "toggleActive", "active", "ScrollContain", "contentSize", "snapsAligned", "containScroll", "pixelTolerance", "scrollBounds", "snapsBounded", "measureBounded", "scrollContainLimit", "findScrollContainLimit", "snapsContained", "measureContained", "usePixelTolerance", "bound", "snap", "startSnap", "endSnap", "lastIndexOf", "snapAligned", "<PERSON><PERSON><PERSON><PERSON>", "isLast", "scrollBound", "parseFloat", "toFixed", "slice", "ScrollLimit", "scrollSnaps", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "vectors", "jointSafety", "shouldLoop", "loopDistance", "v", "ScrollProgress", "ScrollSnaps", "alignment", "containerRect", "slideRects", "slidesToScroll", "groupSlides", "alignments", "measureSizes", "snaps", "measureUnaligned", "measureAligned", "rects", "rect", "g", "SlideRegistry", "containSnaps", "slideIndexes", "slideRegistry", "createSlideRegistry", "groupedSlideIndexes", "doNotContain", "group", "groups", "range", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "targetVector", "minDistance", "distances", "sort", "a", "b", "findTargetSnap", "ascDiffsToSnaps", "shortcut", "d1", "d2", "targets", "matchingTargets", "t", "diffToSnap", "targetSnapDistance", "reachedBound", "snapDistance", "ScrollTo", "indexCurrent", "indexPrevious", "distanceDiff", "indexDiff", "targetIndex", "SlideFocus", "root", "eventStore", "watchFocus", "focusListenerOptions", "capture", "lastTabPressTime", "nowTime", "Date", "getTime", "scrollLeft", "findIndex", "document", "registerTabPress", "slide", "event", "code", "Vector1D", "initialValue", "value", "normalizeInput", "Translate", "translate", "x", "y", "containerStyle", "style", "previousTarget", "to", "newTarget", "transform", "getAttribute", "removeAttribute", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "slideSizesWithGaps", "roundingSafety", "ascItems", "descItems", "reverse", "loopPoints", "startPoints", "endPoints", "removeSlideSizes", "indexes", "slidesInGap", "gap", "remainingGap", "findSlideBounds", "findLoopPoints", "isEndEdge", "slideBounds", "initial", "altered", "boundEdge", "loopPoint", "slideLocation", "canLoop", "every", "otherIndexes", "shiftLocation", "SlidesHandler", "watchSlides", "mutationObserver", "mutations", "mutation", "MutationObserver", "childList", "SlidesInView", "threshold", "intersectionEntryMap", "inView<PERSON>ache", "notInViewCache", "intersectionObserver", "IntersectionObserver", "parentElement", "createInViewList", "inView", "list", "parseInt", "isIntersecting", "inViewMatch", "notInViewMatch", "SlideSizes", "readEdgeGap", "withEdgeGap", "startGap", "measureStartGap", "endGap", "measureEndGap", "measureWithGaps", "slideRect", "getComputedStyle", "getPropertyValue", "SlidesToScroll", "groupByNumber", "byNumber", "groupSize", "bySize", "rectB", "rectA", "edgeA", "edgeB", "gapA", "gapB", "chunkSize", "currentSize", "previousSize", "Engine", "scrollAxis", "startIndex", "inViewThreshold", "<PERSON><PERSON><PERSON><PERSON>", "scrollLooper", "slideLooper", "<PERSON><PERSON><PERSON><PERSON>", "withinBounds", "hasSettled", "interpolatedLocation", "engine", "startLocation", "scrollProgress", "slidesInView", "slideFocus", "resize<PERSON><PERSON>ler", "scrollSnapList", "<PERSON><PERSON><PERSON>ler", "EventHandler", "api", "getListeners", "e", "on", "cb", "off", "defaultOptions", "breakpoints", "OptionsHandler", "mergeOptions", "optionsA", "optionsB", "optionsAtMedia", "matchedMediaOptions", "media", "matchMedia", "matches", "mediaOption", "optionsMediaQueries", "optionsList", "acc", "mediaQueries", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "optionsHandler", "activePlugins", "plugins", "plugin", "assign", "name", "EmblaCarousel", "userOptions", "userPlugins", "defaultView", "pluginsHandler", "mediaHandlers", "reActivate", "optionsBase", "globalOptions", "pluginList", "pluginApis", "storeElements", "userContainer", "userSlides", "customContainer", "querySelector", "children", "customSlides", "querySelectorAll", "createEngine", "optionsWithoutLoop", "activate", "withOptions", "with<PERSON><PERSON><PERSON>", "query", "offsetParent", "selectedScrollSnap", "deActivate", "jump", "scrollNext", "scrollPrev", "prev", "canScrollNext", "canScrollPrev", "previousScrollSnap", "slidesNotInView", "internalEngine", "containerNode", "slideNodes", "setTimeout"], "mappings": ";;AAIM,SAAUA,QAAQA,CAACC,OAAgB,EAAA;EACvC,OAAO,OAAOA,OAAO,KAAK,QAAQ;AACpC;AAEM,SAAUC,QAAQA,CAACD,OAAgB,EAAA;EACvC,OAAO,OAAOA,OAAO,KAAK,QAAQ;AACpC;AAEM,SAAUE,SAASA,CAACF,OAAgB,EAAA;EACxC,OAAO,OAAOA,OAAO,KAAK,SAAS;AACrC;AAEM,SAAUG,QAAQA,CAACH,OAAgB,EAAA;EACvC,OAAOI,MAAM,CAACC,SAAS,CAACC,QAAQ,CAACC,IAAI,CAACP,OAAO,CAAC,KAAK,iBAAiB;AACtE;AAEM,SAAUQ,OAAOA,CAACC,CAAS,EAAA;AAC/B,EAAA,OAAOC,IAAI,CAACC,GAAG,CAACF,CAAC,CAAC;AACpB;AAEM,SAAUG,QAAQA,CAACH,CAAS,EAAA;AAChC,EAAA,OAAOC,IAAI,CAACG,IAAI,CAACJ,CAAC,CAAC;AACrB;AAEgB,SAAAK,QAAQA,CAACC,MAAc,EAAEC,MAAc,EAAA;AACrD,EAAA,OAAOR,OAAO,CAACO,MAAM,GAAGC,MAAM,CAAC;AACjC;AAEgB,SAAAC,SAASA,CAACF,MAAc,EAAEC,MAAc,EAAA;EACtD,IAAID,MAAM,KAAK,CAAC,IAAIC,MAAM,KAAK,CAAC,EAAE,OAAO,CAAC;EAC1C,IAAIR,OAAO,CAACO,MAAM,CAAC,IAAIP,OAAO,CAACQ,MAAM,CAAC,EAAE,OAAO,CAAC;AAChD,EAAA,MAAME,IAAI,GAAGJ,QAAQ,CAACN,OAAO,CAACO,MAAM,CAAC,EAAEP,OAAO,CAACQ,MAAM,CAAC,CAAC;AACvD,EAAA,OAAOR,OAAO,CAACU,IAAI,GAAGH,MAAM,CAAC;AAC/B;AAEM,SAAUI,kBAAkBA,CAACC,GAAW,EAAA;EAC5C,OAAOV,IAAI,CAACW,KAAK,CAACD,GAAG,GAAG,GAAG,CAAC,GAAG,GAAG;AACpC;AAEM,SAAUE,SAASA,CAAOC,KAAa,EAAA;EAC3C,OAAOC,UAAU,CAACD,KAAK,CAAC,CAACE,GAAG,CAACC,MAAM,CAAC;AACtC;AAEM,SAAUC,SAASA,CAAOJ,KAAa,EAAA;AAC3C,EAAA,OAAOA,KAAK,CAACK,cAAc,CAACL,KAAK,CAAC,CAAC;AACrC;AAEM,SAAUK,cAAcA,CAAOL,KAAa,EAAA;EAChD,OAAOb,IAAI,CAACmB,GAAG,CAAC,CAAC,EAAEN,KAAK,CAACO,MAAM,GAAG,CAAC,CAAC;AACtC;AAEgB,SAAAC,gBAAgBA,CAAOR,KAAa,EAAES,KAAa,EAAA;AACjE,EAAA,OAAOA,KAAK,KAAKJ,cAAc,CAACL,KAAK,CAAC;AACxC;SAEgBU,eAAeA,CAACxB,CAAS,EAAEyB,UAAkB,CAAC,EAAA;AAC5D,EAAA,OAAOC,KAAK,CAACC,IAAI,CAACD,KAAK,CAAC1B,CAAC,CAAC,EAAE,CAAC4B,CAAC,EAAEC,CAAC,KAAKJ,OAAO,GAAGI,CAAC,CAAC;AACpD;AAEM,SAAUd,UAAUA,CAAsBe,MAAY,EAAA;AAC1D,EAAA,OAAOnC,MAAM,CAACoC,IAAI,CAACD,MAAM,CAAC;AAC5B;AAEgB,SAAAE,gBAAgBA,CAC9BC,OAAgC,EAChCC,OAAgC,EAAA;AAEhC,EAAA,OAAO,CAACD,OAAO,EAAEC,OAAO,CAAC,CAACC,MAAM,CAAC,CAACC,aAAa,EAAEC,aAAa,KAAI;AAChEtB,IAAAA,UAAU,CAACsB,aAAa,CAAC,CAACC,OAAO,CAAEC,GAAG,IAAI;AACxC,MAAA,MAAMhC,MAAM,GAAG6B,aAAa,CAACG,GAAG,CAAC;AACjC,MAAA,MAAMjC,MAAM,GAAG+B,aAAa,CAACE,GAAG,CAAC;MACjC,MAAMC,UAAU,GAAG9C,QAAQ,CAACa,MAAM,CAAC,IAAIb,QAAQ,CAACY,MAAM,CAAC;AAEvD8B,MAAAA,aAAa,CAACG,GAAG,CAAC,GAAGC,UAAU,GAC3BR,gBAAgB,CAACzB,MAAM,EAAED,MAAM,CAAC,GAChCA,MAAM;AACZ,KAAC,CAAC;AACF,IAAA,OAAO8B,aAAa;GACrB,EAAE,EAAE,CAAC;AACR;AAEgB,SAAAK,YAAYA,CAC1BC,GAAqB,EACrBC,WAAuB,EAAA;EAEvB,OACE,OAAOA,WAAW,CAACC,UAAU,KAAK,WAAW,IAC7CF,GAAG,YAAYC,WAAW,CAACC,UAAU;AAEzC;;ACjFgB,SAAAC,SAASA,CACvBC,KAA0B,EAC1BC,QAAgB,EAAA;AAEhB,EAAA,MAAMC,UAAU,GAAG;IAAEC,KAAK;IAAEC,MAAM;AAAEC,IAAAA;GAAK;EAEzC,SAASF,KAAKA,GAAA;AACZ,IAAA,OAAO,CAAC;AACV;EAEA,SAASC,MAAMA,CAAClD,CAAS,EAAA;AACvB,IAAA,OAAOmD,GAAG,CAACnD,CAAC,CAAC,GAAG,CAAC;AACnB;EAEA,SAASmD,GAAGA,CAACnD,CAAS,EAAA;IACpB,OAAO+C,QAAQ,GAAG/C,CAAC;AACrB;AAEA,EAAA,SAASoD,OAAOA,CAACpD,CAAS,EAAEuB,KAAa,EAAA;AACvC,IAAA,IAAI/B,QAAQ,CAACsD,KAAK,CAAC,EAAE,OAAOE,UAAU,CAACF,KAAK,CAAC,CAAC9C,CAAC,CAAC;AAChD,IAAA,OAAO8C,KAAK,CAACC,QAAQ,EAAE/C,CAAC,EAAEuB,KAAK,CAAC;AAClC;AAEA,EAAA,MAAM8B,IAAI,GAAkB;AAC1BD,IAAAA;GACD;AACD,EAAA,OAAOC,IAAI;AACb;;SCxBgBC,UAAUA,GAAA;EACxB,IAAIC,SAAS,GAAuB,EAAE;EAEtC,SAASC,GAAGA,CACVC,IAAiB,EACjBC,IAAmB,EACnBC,OAAyB,EACzBC,OAA4B,GAAA;AAAEC,IAAAA,OAAO,EAAE;AAAM,GAAA,EAAA;AAE7C,IAAA,IAAIC,cAAgC;IAEpC,IAAI,kBAAkB,IAAIL,IAAI,EAAE;MAC9BA,IAAI,CAACM,gBAAgB,CAACL,IAAI,EAAEC,OAAO,EAAEC,OAAO,CAAC;AAC7CE,MAAAA,cAAc,GAAGA,MAAML,IAAI,CAACO,mBAAmB,CAACN,IAAI,EAAEC,OAAO,EAAEC,OAAO,CAAC;AACzE,KAAC,MAAM;MACL,MAAMK,oBAAoB,GAAmBR,IAAI;AACjDQ,MAAAA,oBAAoB,CAACC,WAAW,CAACP,OAAO,CAAC;MACzCG,cAAc,GAAGA,MAAMG,oBAAoB,CAACH,cAAc,CAACH,OAAO,CAAC;AACrE;AAEAJ,IAAAA,SAAS,CAACY,IAAI,CAACL,cAAc,CAAC;AAC9B,IAAA,OAAOT,IAAI;AACb;EAEA,SAASe,KAAKA,GAAA;IACZb,SAAS,GAAGA,SAAS,CAACc,MAAM,CAAEC,MAAM,IAAKA,MAAM,EAAE,CAAC;AACpD;AAEA,EAAA,MAAMjB,IAAI,GAAmB;IAC3BG,GAAG;AACHY,IAAAA;GACD;AACD,EAAA,OAAOf,IAAI;AACb;;AChCM,SAAUkB,UAAUA,CACxBC,aAAuB,EACvB7B,WAAuB,EACvB8B,MAAkB,EAClBC,MAA+B,EAAA;AAE/B,EAAA,MAAMC,sBAAsB,GAAGrB,UAAU,EAAE;AAC3C,EAAA,MAAMsB,aAAa,GAAG,IAAI,GAAG,EAAE;EAE/B,IAAIC,aAAa,GAAkB,IAAI;EACvC,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIC,WAAW,GAAG,CAAC;EAEnB,SAASC,IAAIA,GAAA;AACXL,IAAAA,sBAAsB,CAACnB,GAAG,CAACgB,aAAa,EAAE,kBAAkB,EAAE,MAAK;AACjE,MAAA,IAAIA,aAAa,CAACS,MAAM,EAAEC,KAAK,EAAE;AACnC,KAAC,CAAC;AACJ;EAEA,SAASC,OAAOA,GAAA;AACdC,IAAAA,IAAI,EAAE;IACNT,sBAAsB,CAACP,KAAK,EAAE;AAChC;EAEA,SAASiB,OAAOA,CAACC,SAA8B,EAAA;IAC7C,IAAI,CAACP,WAAW,EAAE;AAClB,IAAA,IAAI,CAACF,aAAa,EAAEA,aAAa,GAAGS,SAAS;AAE7C,IAAA,MAAMC,WAAW,GAAGD,SAAS,GAAGT,aAAa;AAC7CA,IAAAA,aAAa,GAAGS,SAAS;AACzBR,IAAAA,eAAe,IAAIS,WAAW;IAE9B,OAAOT,eAAe,IAAIF,aAAa,EAAE;AACvCH,MAAAA,MAAM,EAAE;AACRK,MAAAA,eAAe,IAAIF,aAAa;AAClC;AAEA,IAAA,MAAMY,KAAK,GAAGV,eAAe,GAAGF,aAAa;IAC7CF,MAAM,CAACc,KAAK,CAAC;AAEb,IAAA,IAAIT,WAAW,EAAE;AACfA,MAAAA,WAAW,GAAGpC,WAAW,CAAC8C,qBAAqB,CAACJ,OAAO,CAAC;AAC1D;AACF;EAEA,SAASpC,KAAKA,GAAA;AACZ,IAAA,IAAI8B,WAAW,EAAE;AACjBA,IAAAA,WAAW,GAAGpC,WAAW,CAAC8C,qBAAqB,CAACJ,OAAO,CAAC;AAC1D;EAEA,SAASD,IAAIA,GAAA;AACXzC,IAAAA,WAAW,CAAC+C,oBAAoB,CAACX,WAAW,CAAC;AAC7CF,IAAAA,aAAa,GAAG,IAAI;AACpBC,IAAAA,eAAe,GAAG,CAAC;AACnBC,IAAAA,WAAW,GAAG,CAAC;AACjB;EAEA,SAASG,KAAKA,GAAA;AACZL,IAAAA,aAAa,GAAG,IAAI;AACpBC,IAAAA,eAAe,GAAG,CAAC;AACrB;AAEA,EAAA,MAAMzB,IAAI,GAAmB;IAC3B2B,IAAI;IACJG,OAAO;IACPlC,KAAK;IACLmC,IAAI;IACJX,MAAM;AACNC,IAAAA;GACD;AACD,EAAA,OAAOrB,IAAI;AACb;;ACxEgB,SAAAsC,IAAIA,CAClBC,IAAoB,EACpBC,gBAAyC,EAAA;AAEzC,EAAA,MAAMC,aAAa,GAAGD,gBAAgB,KAAK,KAAK;AAChD,EAAA,MAAME,UAAU,GAAGH,IAAI,KAAK,GAAG;AAC/B,EAAA,MAAMI,MAAM,GAAGD,UAAU,GAAG,GAAG,GAAG,GAAG;AACrC,EAAA,MAAME,KAAK,GAAGF,UAAU,GAAG,GAAG,GAAG,GAAG;EACpC,MAAM3F,IAAI,GAAG,CAAC2F,UAAU,IAAID,aAAa,GAAG,CAAC,CAAC,GAAG,CAAC;AAClD,EAAA,MAAMI,SAAS,GAAGC,YAAY,EAAE;AAChC,EAAA,MAAMC,OAAO,GAAGC,UAAU,EAAE;EAE5B,SAASC,WAAWA,CAACC,QAAsB,EAAA;IACzC,MAAM;MAAEC,MAAM;AAAEC,MAAAA;AAAO,KAAA,GAAGF,QAAQ;AAClC,IAAA,OAAOR,UAAU,GAAGS,MAAM,GAAGC,KAAK;AACpC;EAEA,SAASN,YAAYA,GAAA;IACnB,IAAIJ,UAAU,EAAE,OAAO,KAAK;AAC5B,IAAA,OAAOD,aAAa,GAAG,OAAO,GAAG,MAAM;AACzC;EAEA,SAASO,UAAUA,GAAA;IACjB,IAAIN,UAAU,EAAE,OAAO,QAAQ;AAC/B,IAAA,OAAOD,aAAa,GAAG,MAAM,GAAG,OAAO;AACzC;EAEA,SAASY,SAASA,CAAC1G,CAAS,EAAA;IAC1B,OAAOA,CAAC,GAAGI,IAAI;AACjB;AAEA,EAAA,MAAMiD,IAAI,GAAa;IACrB2C,MAAM;IACNC,KAAK;IACLC,SAAS;IACTE,OAAO;IACPE,WAAW;AACXI,IAAAA;GACD;AACD,EAAA,OAAOrD,IAAI;AACb;;SC1CgBsD,KAAKA,CAACC,MAAc,CAAC,EAAExF,MAAc,CAAC,EAAA;AACpD,EAAA,MAAMC,MAAM,GAAGtB,OAAO,CAAC6G,GAAG,GAAGxF,GAAG,CAAC;EAEjC,SAASyF,UAAUA,CAAC7G,CAAS,EAAA;IAC3B,OAAOA,CAAC,GAAG4G,GAAG;AAChB;EAEA,SAASE,UAAUA,CAAC9G,CAAS,EAAA;IAC3B,OAAOA,CAAC,GAAGoB,GAAG;AAChB;EAEA,SAAS2F,UAAUA,CAAC/G,CAAS,EAAA;IAC3B,OAAO6G,UAAU,CAAC7G,CAAC,CAAC,IAAI8G,UAAU,CAAC9G,CAAC,CAAC;AACvC;EAEA,SAASgH,SAASA,CAAChH,CAAS,EAAA;AAC1B,IAAA,IAAI,CAAC+G,UAAU,CAAC/G,CAAC,CAAC,EAAE,OAAOA,CAAC;AAC5B,IAAA,OAAO6G,UAAU,CAAC7G,CAAC,CAAC,GAAG4G,GAAG,GAAGxF,GAAG;AAClC;EAEA,SAAS6F,YAAYA,CAACjH,CAAS,EAAA;AAC7B,IAAA,IAAI,CAACqB,MAAM,EAAE,OAAOrB,CAAC;AACrB,IAAA,OAAOA,CAAC,GAAGqB,MAAM,GAAGpB,IAAI,CAACiH,IAAI,CAAC,CAAClH,CAAC,GAAGoB,GAAG,IAAIC,MAAM,CAAC;AACnD;AAEA,EAAA,MAAMgC,IAAI,GAAc;IACtBhC,MAAM;IACND,GAAG;IACHwF,GAAG;IACHI,SAAS;IACTD,UAAU;IACVD,UAAU;IACVD,UAAU;AACVI,IAAAA;GACD;AACD,EAAA,OAAO5D,IAAI;AACb;;SCvCgB8D,OAAOA,CACrB/F,GAAW,EACX6B,KAAa,EACbmE,IAAa,EAAA;EAEb,MAAM;AAAEJ,IAAAA;AAAS,GAAE,GAAGL,KAAK,CAAC,CAAC,EAAEvF,GAAG,CAAC;AACnC,EAAA,MAAMiG,OAAO,GAAGjG,GAAG,GAAG,CAAC;AACvB,EAAA,IAAIkG,OAAO,GAAGC,WAAW,CAACtE,KAAK,CAAC;EAEhC,SAASsE,WAAWA,CAACvH,CAAS,EAAA;AAC5B,IAAA,OAAO,CAACoH,IAAI,GAAGJ,SAAS,CAAChH,CAAC,CAAC,GAAGD,OAAO,CAAC,CAACsH,OAAO,GAAGrH,CAAC,IAAIqH,OAAO,CAAC;AAChE;EAEA,SAASG,GAAGA,GAAA;AACV,IAAA,OAAOF,OAAO;AAChB;EAEA,SAASG,GAAGA,CAACzH,CAAS,EAAA;AACpBsH,IAAAA,OAAO,GAAGC,WAAW,CAACvH,CAAC,CAAC;AACxB,IAAA,OAAOqD,IAAI;AACb;EAEA,SAASG,GAAGA,CAACxD,CAAS,EAAA;IACpB,OAAO0H,KAAK,EAAE,CAACD,GAAG,CAACD,GAAG,EAAE,GAAGxH,CAAC,CAAC;AAC/B;EAEA,SAAS0H,KAAKA,GAAA;IACZ,OAAOP,OAAO,CAAC/F,GAAG,EAAEoG,GAAG,EAAE,EAAEJ,IAAI,CAAC;AAClC;AAEA,EAAA,MAAM/D,IAAI,GAAgB;IACxBmE,GAAG;IACHC,GAAG;IACHjE,GAAG;AACHkE,IAAAA;GACD;AACD,EAAA,OAAOrE,IAAI;AACb;;SCXgBsE,WAAWA,CACzB/B,IAAc,EACdgC,QAAqB,EACrBpD,aAAuB,EACvB7B,WAAuB,EACvBkF,MAAoB,EACpBC,WAA4B,EAC5BC,QAAsB,EACtBC,SAAyB,EACzBC,QAAsB,EACtBC,UAA0B,EAC1BC,YAA8B,EAC9B5G,KAAkB,EAClB6G,YAA8B,EAC9BC,aAAgC,EAChCC,QAAiB,EACjBC,aAAqB,EACrBC,SAAkB,EAClBC,YAAoB,EACpBC,SAAgC,EAAA;EAEhC,MAAM;AAAEzC,IAAAA,KAAK,EAAE0C,SAAS;AAAEjC,IAAAA;AAAS,GAAE,GAAGd,IAAI;EAC5C,MAAMgD,UAAU,GAAG,CAAC,OAAO,EAAE,QAAQ,EAAE,UAAU,CAAC;AAClD,EAAA,MAAMC,eAAe,GAAG;AAAEhF,IAAAA,OAAO,EAAE;GAAO;AAC1C,EAAA,MAAMiF,UAAU,GAAGxF,UAAU,EAAE;AAC/B,EAAA,MAAMyF,UAAU,GAAGzF,UAAU,EAAE;AAC/B,EAAA,MAAM0F,iBAAiB,GAAGrC,KAAK,CAAC,EAAE,EAAE,GAAG,CAAC,CAACK,SAAS,CAACqB,aAAa,CAACjF,OAAO,CAAC,EAAE,CAAC,CAAC;AAC7E,EAAA,MAAM6F,cAAc,GAAG;AAAEC,IAAAA,KAAK,EAAE,GAAG;AAAEC,IAAAA,KAAK,EAAE;GAAK;AACjD,EAAA,MAAMC,cAAc,GAAG;AAAEF,IAAAA,KAAK,EAAE,GAAG;AAAEC,IAAAA,KAAK,EAAE;GAAK;AACjD,EAAA,MAAME,SAAS,GAAGf,QAAQ,GAAG,EAAE,GAAG,EAAE;EAEpC,IAAIgB,QAAQ,GAAG,KAAK;EACpB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,UAAU,GAAG,CAAC;EAClB,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,aAAa,GAAG,KAAK;EACzB,IAAIC,YAAY,GAAG,KAAK;EACxB,IAAIC,OAAO,GAAG,KAAK;EAEnB,SAAS5E,IAAIA,CAAC6E,QAA2B,EAAA;IACvC,IAAI,CAACnB,SAAS,EAAE;IAEhB,SAASoB,aAAaA,CAACpH,GAAqB,EAAA;AAC1C,MAAA,IAAIjD,SAAS,CAACiJ,SAAS,CAAC,IAAIA,SAAS,CAACmB,QAAQ,EAAEnH,GAAG,CAAC,EAAEqH,IAAI,CAACrH,GAAG,CAAC;AACjE;IAEA,MAAMe,IAAI,GAAGmE,QAAQ;AACrBkB,IAAAA,UAAU,CACPtF,GAAG,CAACC,IAAI,EAAE,WAAW,EAAGf,GAAG,IAAKA,GAAG,CAACsH,cAAc,EAAE,EAAEnB,eAAe,CAAC,CACtErF,GAAG,CAACC,IAAI,EAAE,WAAW,EAAE,MAAMwG,SAAS,EAAEpB,eAAe,CAAC,CACxDrF,GAAG,CAACC,IAAI,EAAE,UAAU,EAAE,MAAMwG,SAAS,CAAC,CACtCzG,GAAG,CAACC,IAAI,EAAE,YAAY,EAAEqG,aAAa,CAAC,CACtCtG,GAAG,CAACC,IAAI,EAAE,WAAW,EAAEqG,aAAa,CAAC,CACrCtG,GAAG,CAACC,IAAI,EAAE,aAAa,EAAEyG,EAAE,CAAC,CAC5B1G,GAAG,CAACC,IAAI,EAAE,aAAa,EAAEyG,EAAE,CAAC,CAC5B1G,GAAG,CAACC,IAAI,EAAE,OAAO,EAAE0G,KAAK,EAAE,IAAI,CAAC;AACpC;EAEA,SAAShF,OAAOA,GAAA;IACd2D,UAAU,CAAC1E,KAAK,EAAE;IAClB2E,UAAU,CAAC3E,KAAK,EAAE;AACpB;EAEA,SAASgG,aAAaA,GAAA;AACpB,IAAA,MAAM3G,IAAI,GAAGmG,OAAO,GAAGpF,aAAa,GAAGoD,QAAQ;AAC/CmB,IAAAA,UAAU,CACPvF,GAAG,CAACC,IAAI,EAAE,WAAW,EAAE4G,IAAI,EAAExB,eAAe,CAAC,CAC7CrF,GAAG,CAACC,IAAI,EAAE,UAAU,EAAEyG,EAAE,CAAC,CACzB1G,GAAG,CAACC,IAAI,EAAE,WAAW,EAAE4G,IAAI,EAAExB,eAAe,CAAC,CAC7CrF,GAAG,CAACC,IAAI,EAAE,SAAS,EAAEyG,EAAE,CAAC;AAC7B;EAEA,SAASI,WAAWA,CAAC7G,IAAa,EAAA;AAChC,IAAA,MAAM8G,QAAQ,GAAG9G,IAAI,CAAC8G,QAAQ,IAAI,EAAE;AACpC,IAAA,OAAO3B,UAAU,CAAC4B,QAAQ,CAACD,QAAQ,CAAC;AACtC;EAEA,SAASE,UAAUA,GAAA;AACjB,IAAA,MAAMC,KAAK,GAAGpC,QAAQ,GAAGc,cAAc,GAAGH,cAAc;AACxD,IAAA,MAAMvF,IAAI,GAAGkG,OAAO,GAAG,OAAO,GAAG,OAAO;IACxC,OAAOc,KAAK,CAAChH,IAAI,CAAC;AACpB;AAEA,EAAA,SAASiH,YAAYA,CAACC,KAAa,EAAEC,aAAsB,EAAA;AACzD,IAAA,MAAMC,IAAI,GAAGvJ,KAAK,CAACiC,GAAG,CAACrD,QAAQ,CAACyK,KAAK,CAAC,GAAG,CAAC,CAAC,CAAC;AAC5C,IAAA,MAAMG,SAAS,GAAG5C,YAAY,CAAC6C,UAAU,CAACJ,KAAK,EAAE,CAACtC,QAAQ,CAAC,CAAC2C,QAAQ;IAEpE,IAAI3C,QAAQ,IAAIvI,OAAO,CAAC6K,KAAK,CAAC,GAAG5B,iBAAiB,EAAE,OAAO+B,SAAS;AACpE,IAAA,IAAIvC,SAAS,IAAIqC,aAAa,EAAE,OAAOE,SAAS,GAAG,GAAG;AAEtD,IAAA,OAAO5C,YAAY,CAAC+C,OAAO,CAACJ,IAAI,CAACtD,GAAG,EAAE,EAAE,CAAC,CAAC,CAACyD,QAAQ;AACrD;EAEA,SAASlB,IAAIA,CAACrH,GAAqB,EAAA;AACjC,IAAA,MAAMyI,UAAU,GAAG1I,YAAY,CAACC,GAAG,EAAEC,WAAW,CAAC;AACjDiH,IAAAA,OAAO,GAAGuB,UAAU;IACpBxB,YAAY,GAAGrB,QAAQ,IAAI6C,UAAU,IAAI,CAACzI,GAAG,CAAC0I,OAAO,IAAI9B,QAAQ;AACjEA,IAAAA,QAAQ,GAAGjJ,QAAQ,CAACwH,MAAM,CAACL,GAAG,EAAE,EAAEO,QAAQ,CAACP,GAAG,EAAE,CAAC,IAAI,CAAC;AAEtD,IAAA,IAAI2D,UAAU,IAAIzI,GAAG,CAAC2I,MAAM,KAAK,CAAC,EAAE;AACpC,IAAA,IAAIf,WAAW,CAAC5H,GAAG,CAACmF,MAAiB,CAAC,EAAE;AAExC4B,IAAAA,aAAa,GAAG,IAAI;AACpB3B,IAAAA,WAAW,CAACwD,WAAW,CAAC5I,GAAG,CAAC;IAC5BwF,UAAU,CAACqD,WAAW,CAAC,CAAC,CAAC,CAACC,WAAW,CAAC,CAAC,CAAC;AACxC3D,IAAAA,MAAM,CAACJ,GAAG,CAACM,QAAQ,CAAC;AACpBqC,IAAAA,aAAa,EAAE;AACfb,IAAAA,WAAW,GAAGzB,WAAW,CAAC2D,SAAS,CAAC/I,GAAG,CAAC;IACxC8G,UAAU,GAAG1B,WAAW,CAAC2D,SAAS,CAAC/I,GAAG,EAAEiG,SAAS,CAAC;AAClDP,IAAAA,YAAY,CAACsD,IAAI,CAAC,aAAa,CAAC;AAClC;EAEA,SAASrB,IAAIA,CAAC3H,GAAqB,EAAA;IACjC,MAAMiJ,UAAU,GAAG,CAAClJ,YAAY,CAACC,GAAG,EAAEC,WAAW,CAAC;AAClD,IAAA,IAAIgJ,UAAU,IAAIjJ,GAAG,CAACkJ,OAAO,CAACvK,MAAM,IAAI,CAAC,EAAE,OAAO6I,EAAE,CAACxH,GAAG,CAAC;AAEzD,IAAA,MAAMmJ,UAAU,GAAG/D,WAAW,CAAC2D,SAAS,CAAC/I,GAAG,CAAC;IAC7C,MAAMoJ,SAAS,GAAGhE,WAAW,CAAC2D,SAAS,CAAC/I,GAAG,EAAEiG,SAAS,CAAC;AACvD,IAAA,MAAMoD,UAAU,GAAG1L,QAAQ,CAACwL,UAAU,EAAEtC,WAAW,CAAC;AACpD,IAAA,MAAMyC,SAAS,GAAG3L,QAAQ,CAACyL,SAAS,EAAEtC,UAAU,CAAC;AAEjD,IAAA,IAAI,CAACE,aAAa,IAAI,CAACE,OAAO,EAAE;MAC9B,IAAI,CAAClH,GAAG,CAACuJ,UAAU,EAAE,OAAO/B,EAAE,CAACxH,GAAG,CAAC;MACnCgH,aAAa,GAAGqC,UAAU,GAAGC,SAAS;AACtC,MAAA,IAAI,CAACtC,aAAa,EAAE,OAAOQ,EAAE,CAACxH,GAAG,CAAC;AACpC;AACA,IAAA,MAAMjC,IAAI,GAAGqH,WAAW,CAACoE,WAAW,CAACxJ,GAAG,CAAC;AACzC,IAAA,IAAIqJ,UAAU,GAAGxD,aAAa,EAAEoB,YAAY,GAAG,IAAI;IAEnDzB,UAAU,CAACqD,WAAW,CAAC,GAAG,CAAC,CAACC,WAAW,CAAC,IAAI,CAAC;IAC7CxD,SAAS,CAAC/E,KAAK,EAAE;AACjB4E,IAAAA,MAAM,CAACrE,GAAG,CAACkD,SAAS,CAACjG,IAAI,CAAC,CAAC;IAC3BiC,GAAG,CAACsH,cAAc,EAAE;AACtB;EAEA,SAASE,EAAEA,CAACxH,GAAqB,EAAA;IAC/B,MAAMyJ,eAAe,GAAGhE,YAAY,CAAC6C,UAAU,CAAC,CAAC,EAAE,KAAK,CAAC;IACzD,MAAMH,aAAa,GAAGsB,eAAe,CAAC5K,KAAK,KAAKA,KAAK,CAACiG,GAAG,EAAE;IAC3D,MAAM4E,QAAQ,GAAGtE,WAAW,CAACuE,SAAS,CAAC3J,GAAG,CAAC,GAAG+H,UAAU,EAAE;IAC1D,MAAMG,KAAK,GAAGD,YAAY,CAACjE,SAAS,CAAC0F,QAAQ,CAAC,EAAEvB,aAAa,CAAC;AAC9D,IAAA,MAAMyB,WAAW,GAAG9L,SAAS,CAAC4L,QAAQ,EAAExB,KAAK,CAAC;AAC9C,IAAA,MAAM2B,KAAK,GAAGlD,SAAS,GAAG,EAAE,GAAGiD,WAAW;AAC1C,IAAA,MAAME,QAAQ,GAAG/D,YAAY,GAAG6D,WAAW,GAAG,EAAE;AAEhD5C,IAAAA,aAAa,GAAG,KAAK;AACrBD,IAAAA,aAAa,GAAG,KAAK;IACrBV,UAAU,CAAC3E,KAAK,EAAE;IAClB8D,UAAU,CAACsD,WAAW,CAACe,KAAK,CAAC,CAAChB,WAAW,CAACiB,QAAQ,CAAC;AACnDvE,IAAAA,QAAQ,CAACgD,QAAQ,CAACL,KAAK,EAAE,CAACtC,QAAQ,CAAC;AACnCsB,IAAAA,OAAO,GAAG,KAAK;AACfxB,IAAAA,YAAY,CAACsD,IAAI,CAAC,WAAW,CAAC;AAChC;EAEA,SAASvB,KAAKA,CAACzH,GAAe,EAAA;AAC5B,IAAA,IAAIiH,YAAY,EAAE;MAChBjH,GAAG,CAAC+J,eAAe,EAAE;MACrB/J,GAAG,CAACsH,cAAc,EAAE;AACpBL,MAAAA,YAAY,GAAG,KAAK;AACtB;AACF;EAEA,SAAS2B,WAAWA,GAAA;AAClB,IAAA,OAAO7B,aAAa;AACtB;AAEA,EAAA,MAAMpG,IAAI,GAAoB;IAC5B2B,IAAI;IACJG,OAAO;AACPmG,IAAAA;GACD;AACD,EAAA,OAAOjI,IAAI;AACb;;AClMgB,SAAAqJ,WAAWA,CACzB9G,IAAc,EACdjD,WAAuB,EAAA;EAEvB,MAAMgK,WAAW,GAAG,GAAG;AAEvB,EAAA,IAAIC,UAA4B;AAChC,EAAA,IAAIC,SAA2B;EAE/B,SAASC,QAAQA,CAACpK,GAAqB,EAAA;IACrC,OAAOA,GAAG,CAAC4C,SAAS;AACtB;AAEA,EAAA,SAASmG,SAASA,CAAC/I,GAAqB,EAAEqK,OAAwB,EAAA;AAChE,IAAA,MAAMC,QAAQ,GAAGD,OAAO,IAAInH,IAAI,CAACI,MAAM;IACvC,MAAMiH,KAAK,GAAqB,CAAA,MAAA,EAASD,QAAQ,KAAK,GAAG,GAAG,GAAG,GAAG,GAAG,CAAE,CAAA;AACvE,IAAA,OAAO,CAACvK,YAAY,CAACC,GAAG,EAAEC,WAAW,CAAC,GAAGD,GAAG,GAAGA,GAAG,CAACkJ,OAAO,CAAC,CAAC,CAAC,EAAEqB,KAAK,CAAC;AACvE;EAEA,SAAS3B,WAAWA,CAAC5I,GAAqB,EAAA;AACxCkK,IAAAA,UAAU,GAAGlK,GAAG;AAChBmK,IAAAA,SAAS,GAAGnK,GAAG;IACf,OAAO+I,SAAS,CAAC/I,GAAG,CAAC;AACvB;EAEA,SAASwJ,WAAWA,CAACxJ,GAAqB,EAAA;IACxC,MAAMjC,IAAI,GAAGgL,SAAS,CAAC/I,GAAG,CAAC,GAAG+I,SAAS,CAACoB,SAAS,CAAC;AAClD,IAAA,MAAMK,OAAO,GAAGJ,QAAQ,CAACpK,GAAG,CAAC,GAAGoK,QAAQ,CAACF,UAAU,CAAC,GAAGD,WAAW;AAElEE,IAAAA,SAAS,GAAGnK,GAAG;AACf,IAAA,IAAIwK,OAAO,EAAEN,UAAU,GAAGlK,GAAG;AAC7B,IAAA,OAAOjC,IAAI;AACb;EAEA,SAAS4L,SAASA,CAAC3J,GAAqB,EAAA;AACtC,IAAA,IAAI,CAACkK,UAAU,IAAI,CAACC,SAAS,EAAE,OAAO,CAAC;IACvC,MAAMM,QAAQ,GAAG1B,SAAS,CAACoB,SAAS,CAAC,GAAGpB,SAAS,CAACmB,UAAU,CAAC;IAC7D,MAAMQ,QAAQ,GAAGN,QAAQ,CAACpK,GAAG,CAAC,GAAGoK,QAAQ,CAACF,UAAU,CAAC;AACrD,IAAA,MAAMM,OAAO,GAAGJ,QAAQ,CAACpK,GAAG,CAAC,GAAGoK,QAAQ,CAACD,SAAS,CAAC,GAAGF,WAAW;AACjE,IAAA,MAAM/B,KAAK,GAAGuC,QAAQ,GAAGC,QAAQ;AACjC,IAAA,MAAMC,OAAO,GAAGD,QAAQ,IAAI,CAACF,OAAO,IAAInN,OAAO,CAAC6K,KAAK,CAAC,GAAG,GAAG;AAE5D,IAAA,OAAOyC,OAAO,GAAGzC,KAAK,GAAG,CAAC;AAC5B;AAEA,EAAA,MAAMvH,IAAI,GAAoB;IAC5BiI,WAAW;IACXY,WAAW;IACXG,SAAS;AACTZ,IAAAA;GACD;AACD,EAAA,OAAOpI,IAAI;AACb;;SCpDgBiK,SAASA,GAAA;EACvB,SAASlK,OAAOA,CAACK,IAAiB,EAAA;IAChC,MAAM;MAAE8J,SAAS;MAAEC,UAAU;MAAEC,WAAW;AAAEC,MAAAA;AAAY,KAAE,GAAGjK,IAAI;AACjE,IAAA,MAAMkK,MAAM,GAAiB;AAC3BC,MAAAA,GAAG,EAAEL,SAAS;MACdM,KAAK,EAAEL,UAAU,GAAGC,WAAW;MAC/BK,MAAM,EAAEP,SAAS,GAAGG,YAAY;AAChCK,MAAAA,IAAI,EAAEP,UAAU;AAChB/G,MAAAA,KAAK,EAAEgH,WAAW;AAClBjH,MAAAA,MAAM,EAAEkH;KACT;AAED,IAAA,OAAOC,MAAM;AACf;AAEA,EAAA,MAAMtK,IAAI,GAAkB;AAC1BD,IAAAA;GACD;AACD,EAAA,OAAOC,IAAI;AACb;;AC5BM,SAAU2K,aAAaA,CAACjL,QAAgB,EAAA;EAC5C,SAASK,OAAOA,CAACpD,CAAS,EAAA;AACxB,IAAA,OAAO+C,QAAQ,IAAI/C,CAAC,GAAG,GAAG,CAAC;AAC7B;AAEA,EAAA,MAAMqD,IAAI,GAAsB;AAC9BD,IAAAA;GACD;AACD,EAAA,OAAOC,IAAI;AACb;;ACKgB,SAAA4K,aAAaA,CAC3BC,SAAsB,EACtB9F,YAA8B,EAC9BzF,WAAuB,EACvBwL,MAAqB,EACrBvI,IAAc,EACdwI,WAAoC,EACpCC,SAAwB,EAAA;EAExB,MAAMC,YAAY,GAAG,CAACJ,SAAS,CAAC,CAACK,MAAM,CAACJ,MAAM,CAAC;AAC/C,EAAA,IAAIK,cAA8B;AAClC,EAAA,IAAIC,aAAqB;EACzB,IAAIC,UAAU,GAAa,EAAE;EAC7B,IAAIC,SAAS,GAAG,KAAK;EAErB,SAASC,QAAQA,CAACnL,IAAiB,EAAA;IACjC,OAAOmC,IAAI,CAACU,WAAW,CAAC+H,SAAS,CAACjL,OAAO,CAACK,IAAI,CAAC,CAAC;AAClD;EAEA,SAASuB,IAAIA,CAAC6E,QAA2B,EAAA;IACvC,IAAI,CAACuE,WAAW,EAAE;AAElBK,IAAAA,aAAa,GAAGG,QAAQ,CAACV,SAAS,CAAC;AACnCQ,IAAAA,UAAU,GAAGP,MAAM,CAACnN,GAAG,CAAC4N,QAAQ,CAAC;IAEjC,SAASC,eAAeA,CAACC,OAA8B,EAAA;AACrD,MAAA,KAAK,MAAMC,KAAK,IAAID,OAAO,EAAE;AAC3B,QAAA,IAAIH,SAAS,EAAE;AAEf,QAAA,MAAMK,WAAW,GAAGD,KAAK,CAAClH,MAAM,KAAKqG,SAAS;QAC9C,MAAMe,UAAU,GAAGd,MAAM,CAACe,OAAO,CAAcH,KAAK,CAAClH,MAAM,CAAC;QAC5D,MAAMsH,QAAQ,GAAGH,WAAW,GAAGP,aAAa,GAAGC,UAAU,CAACO,UAAU,CAAC;AACrE,QAAA,MAAMG,OAAO,GAAGR,QAAQ,CAACI,WAAW,GAAGd,SAAS,GAAGC,MAAM,CAACc,UAAU,CAAC,CAAC;AACtE,QAAA,MAAMI,QAAQ,GAAGtP,OAAO,CAACqP,OAAO,GAAGD,QAAQ,CAAC;QAE5C,IAAIE,QAAQ,IAAI,GAAG,EAAE;UACnBxF,QAAQ,CAACyF,MAAM,EAAE;AACjBlH,UAAAA,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;AAE3B,UAAA;AACF;AACF;AACF;AAEA8C,IAAAA,cAAc,GAAG,IAAIe,cAAc,CAAET,OAAO,IAAI;MAC9C,IAAIrP,SAAS,CAAC2O,WAAW,CAAC,IAAIA,WAAW,CAACvE,QAAQ,EAAEiF,OAAO,CAAC,EAAE;QAC5DD,eAAe,CAACC,OAAO,CAAC;AAC1B;AACF,KAAC,CAAC;IAEFnM,WAAW,CAAC8C,qBAAqB,CAAC,MAAK;MACrC6I,YAAY,CAAChM,OAAO,CAAEmB,IAAI,IAAK+K,cAAc,CAACgB,OAAO,CAAC/L,IAAI,CAAC,CAAC;AAC9D,KAAC,CAAC;AACJ;EAEA,SAAS0B,OAAOA,GAAA;AACdwJ,IAAAA,SAAS,GAAG,IAAI;AAChB,IAAA,IAAIH,cAAc,EAAEA,cAAc,CAACiB,UAAU,EAAE;AACjD;AAEA,EAAA,MAAMpM,IAAI,GAAsB;IAC9B2B,IAAI;AACJG,IAAAA;GACD;AACD,EAAA,OAAO9B,IAAI;AACb;;ACpEgB,SAAAqM,UAAUA,CACxB3H,QAAsB,EACtB4H,cAA4B,EAC5BC,gBAA8B,EAC9B/H,MAAoB,EACpBgI,YAAoB,EACpBpH,YAAoB,EAAA;EAEpB,IAAIqH,cAAc,GAAG,CAAC;EACtB,IAAIC,eAAe,GAAG,CAAC;EACvB,IAAIC,cAAc,GAAGH,YAAY;EACjC,IAAII,cAAc,GAAGxH,YAAY;AACjC,EAAA,IAAIyH,WAAW,GAAGnI,QAAQ,CAACP,GAAG,EAAE;EAChC,IAAI2I,mBAAmB,GAAG,CAAC;EAE3B,SAASC,IAAIA,GAAA;AACX,IAAA,MAAMC,YAAY,GAAGxI,MAAM,CAACL,GAAG,EAAE,GAAGO,QAAQ,CAACP,GAAG,EAAE;IAClD,MAAM8I,SAAS,GAAG,CAACN,cAAc;IACjC,IAAIO,cAAc,GAAG,CAAC;AAEtB,IAAA,IAAID,SAAS,EAAE;AACbR,MAAAA,cAAc,GAAG,CAAC;AAClBF,MAAAA,gBAAgB,CAACnI,GAAG,CAACI,MAAM,CAAC;AAC5BE,MAAAA,QAAQ,CAACN,GAAG,CAACI,MAAM,CAAC;AAEpB0I,MAAAA,cAAc,GAAGF,YAAY;AAC/B,KAAC,MAAM;AACLT,MAAAA,gBAAgB,CAACnI,GAAG,CAACM,QAAQ,CAAC;MAE9B+H,cAAc,IAAIO,YAAY,GAAGL,cAAc;AAC/CF,MAAAA,cAAc,IAAIG,cAAc;AAChCC,MAAAA,WAAW,IAAIJ,cAAc;AAC7B/H,MAAAA,QAAQ,CAACvE,GAAG,CAACsM,cAAc,CAAC;MAE5BS,cAAc,GAAGL,WAAW,GAAGC,mBAAmB;AACpD;AAEAJ,IAAAA,eAAe,GAAG5P,QAAQ,CAACoQ,cAAc,CAAC;AAC1CJ,IAAAA,mBAAmB,GAAGD,WAAW;AACjC,IAAA,OAAO7M,IAAI;AACb;EAEA,SAASmN,OAAOA,GAAA;AACd,IAAA,MAAM/P,IAAI,GAAGoH,MAAM,CAACL,GAAG,EAAE,GAAGmI,cAAc,CAACnI,GAAG,EAAE;AAChD,IAAA,OAAOzH,OAAO,CAACU,IAAI,CAAC,GAAG,KAAK;AAC9B;EAEA,SAASgQ,QAAQA,GAAA;AACf,IAAA,OAAOT,cAAc;AACvB;EAEA,SAAStJ,SAASA,GAAA;AAChB,IAAA,OAAOqJ,eAAe;AACxB;EAEA,SAASW,QAAQA,GAAA;AACf,IAAA,OAAOZ,cAAc;AACvB;EAEA,SAASa,eAAeA,GAAA;IACtB,OAAOnF,WAAW,CAACqE,YAAY,CAAC;AAClC;EAEA,SAASe,eAAeA,GAAA;IACtB,OAAOrF,WAAW,CAAC9C,YAAY,CAAC;AAClC;EAEA,SAAS+C,WAAWA,CAACxL,CAAS,EAAA;AAC5BgQ,IAAAA,cAAc,GAAGhQ,CAAC;AAClB,IAAA,OAAOqD,IAAI;AACb;EAEA,SAASkI,WAAWA,CAACvL,CAAS,EAAA;AAC5BiQ,IAAAA,cAAc,GAAGjQ,CAAC;AAClB,IAAA,OAAOqD,IAAI;AACb;AAEA,EAAA,MAAMA,IAAI,GAAmB;IAC3BqD,SAAS;IACT+J,QAAQ;IACRC,QAAQ;IACRN,IAAI;IACJI,OAAO;IACPI,eAAe;IACfD,eAAe;IACfpF,WAAW;AACXC,IAAAA;GACD;AACD,EAAA,OAAOnI,IAAI;AACb;;AC5FM,SAAUwN,YAAYA,CAC1BC,KAAgB,EAChB/I,QAAsB,EACtBF,MAAoB,EACpBK,UAA0B,EAC1BG,aAAgC,EAAA;AAEhC,EAAA,MAAM0I,iBAAiB,GAAG1I,aAAa,CAACjF,OAAO,CAAC,EAAE,CAAC;AACnD,EAAA,MAAM4N,mBAAmB,GAAG3I,aAAa,CAACjF,OAAO,CAAC,EAAE,CAAC;AACrD,EAAA,MAAM6N,aAAa,GAAGtK,KAAK,CAAC,GAAG,EAAE,IAAI,CAAC;EACtC,IAAIuK,QAAQ,GAAG,KAAK;EAEpB,SAASC,eAAeA,GAAA;IACtB,IAAID,QAAQ,EAAE,OAAO,KAAK;AAC1B,IAAA,IAAI,CAACJ,KAAK,CAAC/J,UAAU,CAACc,MAAM,CAACL,GAAG,EAAE,CAAC,EAAE,OAAO,KAAK;AACjD,IAAA,IAAI,CAACsJ,KAAK,CAAC/J,UAAU,CAACgB,QAAQ,CAACP,GAAG,EAAE,CAAC,EAAE,OAAO,KAAK;AACnD,IAAA,OAAO,IAAI;AACb;EAEA,SAASR,SAASA,CAACsE,WAAoB,EAAA;AACrC,IAAA,IAAI,CAAC6F,eAAe,EAAE,EAAE;AACxB,IAAA,MAAMC,IAAI,GAAGN,KAAK,CAACjK,UAAU,CAACkB,QAAQ,CAACP,GAAG,EAAE,CAAC,GAAG,KAAK,GAAG,KAAK;AAC7D,IAAA,MAAM6J,UAAU,GAAGtR,OAAO,CAAC+Q,KAAK,CAACM,IAAI,CAAC,GAAGrJ,QAAQ,CAACP,GAAG,EAAE,CAAC;AACxD,IAAA,MAAM8J,YAAY,GAAGzJ,MAAM,CAACL,GAAG,EAAE,GAAGO,QAAQ,CAACP,GAAG,EAAE;IAClD,MAAMgF,QAAQ,GAAGyE,aAAa,CAACjK,SAAS,CAACqK,UAAU,GAAGL,mBAAmB,CAAC;AAE1EnJ,IAAAA,MAAM,CAAC0J,QAAQ,CAACD,YAAY,GAAG9E,QAAQ,CAAC;IAExC,IAAI,CAAClB,WAAW,IAAIvL,OAAO,CAACuR,YAAY,CAAC,GAAGP,iBAAiB,EAAE;AAC7DlJ,MAAAA,MAAM,CAACJ,GAAG,CAACqJ,KAAK,CAAC9J,SAAS,CAACa,MAAM,CAACL,GAAG,EAAE,CAAC,CAAC;MACzCU,UAAU,CAACsD,WAAW,CAAC,EAAE,CAAC,CAACoF,eAAe,EAAE;AAC9C;AACF;EAEA,SAASY,YAAYA,CAACC,MAAe,EAAA;IACnCP,QAAQ,GAAG,CAACO,MAAM;AACpB;AAEA,EAAA,MAAMpO,IAAI,GAAqB;IAC7B8N,eAAe;IACfnK,SAAS;AACTwK,IAAAA;GACD;AACD,EAAA,OAAOnO,IAAI;AACb;;AC9CM,SAAUqO,aAAaA,CAC3B3O,QAAgB,EAChB4O,WAAmB,EACnBC,YAAsB,EACtBC,aAAsC,EACtCC,cAAsB,EAAA;EAEtB,MAAMC,YAAY,GAAGpL,KAAK,CAAC,CAACgL,WAAW,GAAG5O,QAAQ,EAAE,CAAC,CAAC;AACtD,EAAA,MAAMiP,YAAY,GAAGC,cAAc,EAAE;AACrC,EAAA,MAAMC,kBAAkB,GAAGC,sBAAsB,EAAE;AACnD,EAAA,MAAMC,cAAc,GAAGC,gBAAgB,EAAE;AAEzC,EAAA,SAASC,iBAAiBA,CAACC,KAAa,EAAEC,IAAY,EAAA;AACpD,IAAA,OAAOnS,QAAQ,CAACkS,KAAK,EAAEC,IAAI,CAAC,GAAG,CAAC;AAClC;EAEA,SAASL,sBAAsBA,GAAA;AAC7B,IAAA,MAAMM,SAAS,GAAGT,YAAY,CAAC,CAAC,CAAC;AACjC,IAAA,MAAMU,OAAO,GAAGxR,SAAS,CAAC8Q,YAAY,CAAC;AACvC,IAAA,MAAMpL,GAAG,GAAGoL,YAAY,CAACW,WAAW,CAACF,SAAS,CAAC;IAC/C,MAAMrR,GAAG,GAAG4Q,YAAY,CAAC9C,OAAO,CAACwD,OAAO,CAAC,GAAG,CAAC;AAC7C,IAAA,OAAO/L,KAAK,CAACC,GAAG,EAAExF,GAAG,CAAC;AACxB;EAEA,SAAS6Q,cAAcA,GAAA;IACrB,OAAOL,YAAY,CAChB5Q,GAAG,CAAC,CAAC4R,WAAW,EAAErR,KAAK,KAAI;MAC1B,MAAM;QAAEqF,GAAG;AAAExF,QAAAA;AAAK,OAAA,GAAG2Q,YAAY;AACjC,MAAA,MAAMS,IAAI,GAAGT,YAAY,CAAC/K,SAAS,CAAC4L,WAAW,CAAC;MAChD,MAAMC,OAAO,GAAG,CAACtR,KAAK;AACtB,MAAA,MAAMuR,MAAM,GAAGxR,gBAAgB,CAACsQ,YAAY,EAAErQ,KAAK,CAAC;MACpD,IAAIsR,OAAO,EAAE,OAAOzR,GAAG;MACvB,IAAI0R,MAAM,EAAE,OAAOlM,GAAG;MACtB,IAAI0L,iBAAiB,CAAC1L,GAAG,EAAE4L,IAAI,CAAC,EAAE,OAAO5L,GAAG;MAC5C,IAAI0L,iBAAiB,CAAClR,GAAG,EAAEoR,IAAI,CAAC,EAAE,OAAOpR,GAAG;AAC5C,MAAA,OAAOoR,IAAI;AACb,KAAC,CAAC,CACDxR,GAAG,CAAE+R,WAAW,IAAKC,UAAU,CAACD,WAAW,CAACE,OAAO,CAAC,CAAC,CAAC,CAAC,CAAC;AAC7D;EAEA,SAASZ,gBAAgBA,GAAA;IACvB,IAAIV,WAAW,IAAI5O,QAAQ,GAAG+O,cAAc,EAAE,OAAO,CAACC,YAAY,CAAC3Q,GAAG,CAAC;AACvE,IAAA,IAAIyQ,aAAa,KAAK,WAAW,EAAE,OAAOG,YAAY;IACtD,MAAM;MAAEpL,GAAG;AAAExF,MAAAA;AAAK,KAAA,GAAG8Q,kBAAkB;AACvC,IAAA,OAAOF,YAAY,CAACkB,KAAK,CAACtM,GAAG,EAAExF,GAAG,CAAC;AACrC;AAEA,EAAA,MAAMiC,IAAI,GAAsB;IAC9B+O,cAAc;AACdF,IAAAA;GACD;AACD,EAAA,OAAO7O,IAAI;AACb;;SCvDgB8P,WAAWA,CACzBxB,WAAmB,EACnByB,WAAqB,EACrBhM,IAAa,EAAA;AAEb,EAAA,MAAMhG,GAAG,GAAGgS,WAAW,CAAC,CAAC,CAAC;EAC1B,MAAMxM,GAAG,GAAGQ,IAAI,GAAGhG,GAAG,GAAGuQ,WAAW,GAAGzQ,SAAS,CAACkS,WAAW,CAAC;AAC7D,EAAA,MAAMtC,KAAK,GAAGnK,KAAK,CAACC,GAAG,EAAExF,GAAG,CAAC;AAE7B,EAAA,MAAMiC,IAAI,GAAoB;AAC5ByN,IAAAA;GACD;AACD,EAAA,OAAOzN,IAAI;AACb;;ACbM,SAAUgQ,YAAYA,CAC1B1B,WAAmB,EACnBb,KAAgB,EAChB/I,QAAsB,EACtBuL,OAAuB,EAAA;EAEvB,MAAMC,WAAW,GAAG,GAAG;AACvB,EAAA,MAAM3M,GAAG,GAAGkK,KAAK,CAAClK,GAAG,GAAG2M,WAAW;AACnC,EAAA,MAAMnS,GAAG,GAAG0P,KAAK,CAAC1P,GAAG,GAAGmS,WAAW;EACnC,MAAM;IAAE1M,UAAU;AAAEC,IAAAA;AAAY,GAAA,GAAGH,KAAK,CAACC,GAAG,EAAExF,GAAG,CAAC;EAElD,SAASoS,UAAUA,CAAC9M,SAAiB,EAAA;AACnC,IAAA,IAAIA,SAAS,KAAK,CAAC,EAAE,OAAOI,UAAU,CAACiB,QAAQ,CAACP,GAAG,EAAE,CAAC;AACtD,IAAA,IAAId,SAAS,KAAK,CAAC,CAAC,EAAE,OAAOG,UAAU,CAACkB,QAAQ,CAACP,GAAG,EAAE,CAAC;AACvD,IAAA,OAAO,KAAK;AACd;EAEA,SAASJ,IAAIA,CAACV,SAAiB,EAAA;AAC7B,IAAA,IAAI,CAAC8M,UAAU,CAAC9M,SAAS,CAAC,EAAE;IAE5B,MAAM+M,YAAY,GAAG9B,WAAW,IAAIjL,SAAS,GAAG,CAAC,CAAC,CAAC;IACnD4M,OAAO,CAAChR,OAAO,CAAEoR,CAAC,IAAKA,CAAC,CAAClQ,GAAG,CAACiQ,YAAY,CAAC,CAAC;AAC7C;AAEA,EAAA,MAAMpQ,IAAI,GAAqB;AAC7B+D,IAAAA;GACD;AACD,EAAA,OAAO/D,IAAI;AACb;;AC7BM,SAAUsQ,cAAcA,CAAC7C,KAAgB,EAAA;EAC7C,MAAM;IAAE1P,GAAG;AAAEC,IAAAA;AAAQ,GAAA,GAAGyP,KAAK;EAE7B,SAAStJ,GAAGA,CAACxH,CAAS,EAAA;AACpB,IAAA,MAAMmM,eAAe,GAAGnM,CAAC,GAAGoB,GAAG;AAC/B,IAAA,OAAOC,MAAM,GAAG8K,eAAe,GAAG,CAAC9K,MAAM,GAAG,CAAC;AAC/C;AAEA,EAAA,MAAMgC,IAAI,GAAuB;AAC/BmE,IAAAA;GACD;AACD,EAAA,OAAOnE,IAAI;AACb;;ACPM,SAAUuQ,WAAWA,CACzBhO,IAAc,EACdiO,SAAwB,EACxBC,aAA2B,EAC3BC,UAA0B,EAC1BC,cAAkC,EAAA;EAElC,MAAM;IAAE9N,SAAS;AAAEE,IAAAA;AAAS,GAAA,GAAGR,IAAI;EACnC,MAAM;AAAEqO,IAAAA;AAAa,GAAA,GAAGD,cAAc;EACtC,MAAME,UAAU,GAAGC,YAAY,EAAE,CAACnT,GAAG,CAAC6S,SAAS,CAACzQ,OAAO,CAAC;AACxD,EAAA,MAAMgR,KAAK,GAAGC,gBAAgB,EAAE;AAChC,EAAA,MAAMzC,YAAY,GAAG0C,cAAc,EAAE;EAErC,SAASH,YAAYA,GAAA;AACnB,IAAA,OAAOF,WAAW,CAACF,UAAU,CAAC,CAC3B/S,GAAG,CAAEuT,KAAK,IAAKrT,SAAS,CAACqT,KAAK,CAAC,CAACnO,OAAO,CAAC,GAAGmO,KAAK,CAAC,CAAC,CAAC,CAACrO,SAAS,CAAC,CAAC,CAC/DlF,GAAG,CAACjB,OAAO,CAAC;AACjB;EAEA,SAASsU,gBAAgBA,GAAA;IACvB,OAAON,UAAU,CACd/S,GAAG,CAAEwT,IAAI,IAAKV,aAAa,CAAC5N,SAAS,CAAC,GAAGsO,IAAI,CAACtO,SAAS,CAAC,CAAC,CACzDlF,GAAG,CAAEwR,IAAI,IAAK,CAACzS,OAAO,CAACyS,IAAI,CAAC,CAAC;AAClC;EAEA,SAAS8B,cAAcA,GAAA;AACrB,IAAA,OAAOL,WAAW,CAACG,KAAK,CAAC,CACtBpT,GAAG,CAAEyT,CAAC,IAAKA,CAAC,CAAC,CAAC,CAAC,CAAC,CAChBzT,GAAG,CAAC,CAACwR,IAAI,EAAEjR,KAAK,KAAKiR,IAAI,GAAG0B,UAAU,CAAC3S,KAAK,CAAC,CAAC;AACnD;AAEA,EAAA,MAAM8B,IAAI,GAAoB;IAC5B+Q,KAAK;AACLxC,IAAAA;GACD;AACD,EAAA,OAAOvO,IAAI;AACb;;ACjCgB,SAAAqR,aAAaA,CAC3BC,YAAqB,EACrB9C,aAAsC,EACtCuB,WAAqB,EACrBlB,kBAA6B,EAC7B8B,cAAkC,EAClCY,YAAsB,EAAA;EAEtB,MAAM;AAAEX,IAAAA;AAAa,GAAA,GAAGD,cAAc;EACtC,MAAM;IAAEpN,GAAG;AAAExF,IAAAA;AAAK,GAAA,GAAG8Q,kBAAkB;AACvC,EAAA,MAAM2C,aAAa,GAAGC,mBAAmB,EAAE;EAE3C,SAASA,mBAAmBA,GAAA;AAC1B,IAAA,MAAMC,mBAAmB,GAAGd,WAAW,CAACW,YAAY,CAAC;AACrD,IAAA,MAAMI,YAAY,GAAG,CAACL,YAAY,IAAI9C,aAAa,KAAK,WAAW;IAEnE,IAAIuB,WAAW,CAAC/R,MAAM,KAAK,CAAC,EAAE,OAAO,CAACuT,YAAY,CAAC;IACnD,IAAII,YAAY,EAAE,OAAOD,mBAAmB;AAE5C,IAAA,OAAOA,mBAAmB,CAAC7B,KAAK,CAACtM,GAAG,EAAExF,GAAG,CAAC,CAACJ,GAAG,CAAC,CAACiU,KAAK,EAAE1T,KAAK,EAAE2T,MAAM,KAAI;MACtE,MAAMrC,OAAO,GAAG,CAACtR,KAAK;AACtB,MAAA,MAAMuR,MAAM,GAAGxR,gBAAgB,CAAC4T,MAAM,EAAE3T,KAAK,CAAC;AAE9C,MAAA,IAAIsR,OAAO,EAAE;QACX,MAAMsC,KAAK,GAAGjU,SAAS,CAACgU,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QACtC,OAAO1T,eAAe,CAAC2T,KAAK,CAAC;AAC/B;AACA,MAAA,IAAIrC,MAAM,EAAE;AACV,QAAA,MAAMqC,KAAK,GAAGhU,cAAc,CAACyT,YAAY,CAAC,GAAG1T,SAAS,CAACgU,MAAM,CAAC,CAAC,CAAC,CAAC,GAAG,CAAC;QACrE,OAAO1T,eAAe,CAAC2T,KAAK,EAAEjU,SAAS,CAACgU,MAAM,CAAC,CAAC,CAAC,CAAC,CAAC;AACrD;AACA,MAAA,OAAOD,KAAK;AACd,KAAC,CAAC;AACJ;AAEA,EAAA,MAAM5R,IAAI,GAAsB;AAC9BwR,IAAAA;GACD;AACD,EAAA,OAAOxR,IAAI;AACb;;ACtCM,SAAU+R,YAAYA,CAC1BhO,IAAa,EACbgM,WAAqB,EACrBzB,WAAmB,EACnBb,KAAgB,EAChBuE,YAA0B,EAAA;EAE1B,MAAM;IAAEtO,UAAU;IAAEE,YAAY;AAAED,IAAAA;AAAS,GAAE,GAAG8J,KAAK;EAErD,SAASwE,WAAWA,CAACC,SAAmB,EAAA;IACtC,OAAOA,SAAS,CAAChH,MAAM,EAAE,CAACiH,IAAI,CAAC,CAACC,CAAC,EAAEC,CAAC,KAAK3V,OAAO,CAAC0V,CAAC,CAAC,GAAG1V,OAAO,CAAC2V,CAAC,CAAC,CAAC,CAAC,CAAC,CAAC;AACtE;EAEA,SAASC,cAAcA,CAAC9N,MAAc,EAAA;AACpC,IAAA,MAAMoD,QAAQ,GAAG7D,IAAI,GAAGH,YAAY,CAACY,MAAM,CAAC,GAAGb,SAAS,CAACa,MAAM,CAAC;IAChE,MAAM+N,eAAe,GAAGxC,WAAW,CAChCpS,GAAG,CAAC,CAACwR,IAAI,EAAEjR,KAAK,MAAM;MAAEd,IAAI,EAAEoV,QAAQ,CAACrD,IAAI,GAAGvH,QAAQ,EAAE,CAAC,CAAC;AAAE1J,MAAAA;KAAO,CAAC,CAAC,CACrEiU,IAAI,CAAC,CAACM,EAAE,EAAEC,EAAE,KAAKhW,OAAO,CAAC+V,EAAE,CAACrV,IAAI,CAAC,GAAGV,OAAO,CAACgW,EAAE,CAACtV,IAAI,CAAC,CAAC;IAExD,MAAM;AAAEc,MAAAA;AAAO,KAAA,GAAGqU,eAAe,CAAC,CAAC,CAAC;IACpC,OAAO;MAAErU,KAAK;AAAE0J,MAAAA;KAAU;AAC5B;AAEA,EAAA,SAAS4K,QAAQA,CAAChO,MAAc,EAAEnB,SAAiB,EAAA;AACjD,IAAA,MAAMsP,OAAO,GAAG,CAACnO,MAAM,EAAEA,MAAM,GAAG8J,WAAW,EAAE9J,MAAM,GAAG8J,WAAW,CAAC;AAEpE,IAAA,IAAI,CAACvK,IAAI,EAAE,OAAOS,MAAM;AACxB,IAAA,IAAI,CAACnB,SAAS,EAAE,OAAO4O,WAAW,CAACU,OAAO,CAAC;AAE3C,IAAA,MAAMC,eAAe,GAAGD,OAAO,CAAC3R,MAAM,CAAE6R,CAAC,IAAK/V,QAAQ,CAAC+V,CAAC,CAAC,KAAKxP,SAAS,CAAC;IACxE,IAAIuP,eAAe,CAAC5U,MAAM,EAAE,OAAOiU,WAAW,CAACW,eAAe,CAAC;AAC/D,IAAA,OAAO/U,SAAS,CAAC8U,OAAO,CAAC,GAAGrE,WAAW;AACzC;AAEA,EAAA,SAASzG,OAAOA,CAAC3J,KAAa,EAAEmF,SAAiB,EAAA;IAC/C,MAAMyP,UAAU,GAAG/C,WAAW,CAAC7R,KAAK,CAAC,GAAG8T,YAAY,CAAC7N,GAAG,EAAE;AAC1D,IAAA,MAAMyD,QAAQ,GAAG4K,QAAQ,CAACM,UAAU,EAAEzP,SAAS,CAAC;IAChD,OAAO;MAAEnF,KAAK;AAAE0J,MAAAA;KAAU;AAC5B;AAEA,EAAA,SAASD,UAAUA,CAACC,QAAgB,EAAEuH,IAAa,EAAA;IACjD,MAAM3K,MAAM,GAAGwN,YAAY,CAAC7N,GAAG,EAAE,GAAGyD,QAAQ;IAC5C,MAAM;MAAE1J,KAAK;AAAE0J,MAAAA,QAAQ,EAAEmL;AAAoB,KAAA,GAAGT,cAAc,CAAC9N,MAAM,CAAC;IACtE,MAAMwO,YAAY,GAAG,CAACjP,IAAI,IAAIL,UAAU,CAACc,MAAM,CAAC;AAEhD,IAAA,IAAI,CAAC2K,IAAI,IAAI6D,YAAY,EAAE,OAAO;MAAE9U,KAAK;AAAE0J,MAAAA;KAAU;AAErD,IAAA,MAAMkL,UAAU,GAAG/C,WAAW,CAAC7R,KAAK,CAAC,GAAG6U,kBAAkB;IAC1D,MAAME,YAAY,GAAGrL,QAAQ,GAAG4K,QAAQ,CAACM,UAAU,EAAE,CAAC,CAAC;IAEvD,OAAO;MAAE5U,KAAK;AAAE0J,MAAAA,QAAQ,EAAEqL;KAAc;AAC1C;AAEA,EAAA,MAAMjT,IAAI,GAAqB;IAC7B2H,UAAU;IACVE,OAAO;AACP2K,IAAAA;GACD;AACD,EAAA,OAAOxS,IAAI;AACb;;AC9DgB,SAAAkT,QAAQA,CACtBvO,SAAyB,EACzBwO,YAAyB,EACzBC,aAA0B,EAC1BvO,UAA0B,EAC1BC,YAA8B,EAC9BkN,YAA0B,EAC1BjN,YAA8B,EAAA;EAE9B,SAASH,QAAQA,CAACJ,MAAkB,EAAA;AAClC,IAAA,MAAM6O,YAAY,GAAG7O,MAAM,CAACoD,QAAQ;IACpC,MAAM0L,SAAS,GAAG9O,MAAM,CAACtG,KAAK,KAAKiV,YAAY,CAAChP,GAAG,EAAE;AAErD6N,IAAAA,YAAY,CAAC7R,GAAG,CAACkT,YAAY,CAAC;AAE9B,IAAA,IAAIA,YAAY,EAAE;AAChB,MAAA,IAAIxO,UAAU,CAACuI,QAAQ,EAAE,EAAE;QACzBzI,SAAS,CAAC/E,KAAK,EAAE;AACnB,OAAC,MAAM;QACL+E,SAAS,CAACvD,MAAM,EAAE;AAClBuD,QAAAA,SAAS,CAACtD,MAAM,CAAC,CAAC,CAAC;QACnBsD,SAAS,CAACvD,MAAM,EAAE;AACpB;AACF;AAEA,IAAA,IAAIkS,SAAS,EAAE;MACbF,aAAa,CAAChP,GAAG,CAAC+O,YAAY,CAAChP,GAAG,EAAE,CAAC;AACrCgP,MAAAA,YAAY,CAAC/O,GAAG,CAACI,MAAM,CAACtG,KAAK,CAAC;AAC9B6G,MAAAA,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;AAC7B;AACF;AAEA,EAAA,SAAST,QAAQA,CAACjL,CAAS,EAAEwS,IAAa,EAAA;IACxC,MAAM3K,MAAM,GAAGM,YAAY,CAAC6C,UAAU,CAAChL,CAAC,EAAEwS,IAAI,CAAC;IAC/CvK,QAAQ,CAACJ,MAAM,CAAC;AAClB;AAEA,EAAA,SAAStG,KAAKA,CAACvB,CAAS,EAAE0G,SAAiB,EAAA;IACzC,MAAMkQ,WAAW,GAAGJ,YAAY,CAAC9O,KAAK,EAAE,CAACD,GAAG,CAACzH,CAAC,CAAC;AAC/C,IAAA,MAAM6H,MAAM,GAAGM,YAAY,CAAC+C,OAAO,CAAC0L,WAAW,CAACpP,GAAG,EAAE,EAAEd,SAAS,CAAC;IACjEuB,QAAQ,CAACJ,MAAM,CAAC;AAClB;AAEA,EAAA,MAAMxE,IAAI,GAAiB;IACzB4H,QAAQ;AACR1J,IAAAA;GACD;AACD,EAAA,OAAO8B,IAAI;AACb;;SCzCgBwT,UAAUA,CACxBC,IAAiB,EACjB3I,MAAqB,EACrB0G,aAAiD,EACjD5M,QAAsB,EACtBC,UAA0B,EAC1B6O,UAA0B,EAC1B3O,YAA8B,EAC9B4O,UAAkC,EAAA;AAElC,EAAA,MAAMC,oBAAoB,GAAG;AAAEpT,IAAAA,OAAO,EAAE,IAAI;AAAEqT,IAAAA,OAAO,EAAE;GAAM;EAC7D,IAAIC,gBAAgB,GAAG,CAAC;EAExB,SAASnS,IAAIA,CAAC6E,QAA2B,EAAA;IACvC,IAAI,CAACmN,UAAU,EAAE;IAEjB,SAASnI,eAAeA,CAACtN,KAAa,EAAA;MACpC,MAAM6V,OAAO,GAAG,IAAIC,IAAI,EAAE,CAACC,OAAO,EAAE;AACpC,MAAA,MAAMlK,QAAQ,GAAGgK,OAAO,GAAGD,gBAAgB;MAE3C,IAAI/J,QAAQ,GAAG,EAAE,EAAE;AAEnBhF,MAAAA,YAAY,CAACsD,IAAI,CAAC,iBAAiB,CAAC;MACpCoL,IAAI,CAACS,UAAU,GAAG,CAAC;AAEnB,MAAA,MAAMtC,KAAK,GAAGJ,aAAa,CAAC2C,SAAS,CAAEvC,KAAK,IAAKA,KAAK,CAACzK,QAAQ,CAACjJ,KAAK,CAAC,CAAC;AAEvE,MAAA,IAAI,CAACjC,QAAQ,CAAC2V,KAAK,CAAC,EAAE;AAEtB/M,MAAAA,UAAU,CAACsD,WAAW,CAAC,CAAC,CAAC;AACzBvD,MAAAA,QAAQ,CAAC1G,KAAK,CAAC0T,KAAK,EAAE,CAAC,CAAC;AAExB7M,MAAAA,YAAY,CAACsD,IAAI,CAAC,YAAY,CAAC;AACjC;IAEAqL,UAAU,CAACvT,GAAG,CAACiU,QAAQ,EAAE,SAAS,EAAEC,gBAAgB,EAAE,KAAK,CAAC;AAE5DvJ,IAAAA,MAAM,CAAC7L,OAAO,CAAC,CAACqV,KAAK,EAAE1I,UAAU,KAAI;MACnC8H,UAAU,CAACvT,GAAG,CACZmU,KAAK,EACL,OAAO,EACNjV,GAAe,IAAI;QAClB,IAAIjD,SAAS,CAACuX,UAAU,CAAC,IAAIA,UAAU,CAACnN,QAAQ,EAAEnH,GAAG,CAAC,EAAE;UACtDmM,eAAe,CAACI,UAAU,CAAC;AAC7B;OACD,EACDgI,oBAAoB,CACrB;AACH,KAAC,CAAC;AACJ;EAEA,SAASS,gBAAgBA,CAACE,KAAoB,EAAA;AAC5C,IAAA,IAAIA,KAAK,CAACC,IAAI,KAAK,KAAK,EAAEV,gBAAgB,GAAG,IAAIE,IAAI,EAAE,CAACC,OAAO,EAAE;AACnE;AAEA,EAAA,MAAMjU,IAAI,GAAmB;AAC3B2B,IAAAA;GACD;AACD,EAAA,OAAO3B,IAAI;AACb;;ACrEM,SAAUyU,QAAQA,CAACC,YAAoB,EAAA;EAC3C,IAAIC,KAAK,GAAGD,YAAY;EAExB,SAASvQ,GAAGA,GAAA;AACV,IAAA,OAAOwQ,KAAK;AACd;EAEA,SAASvQ,GAAGA,CAACzH,CAAwB,EAAA;AACnCgY,IAAAA,KAAK,GAAGC,cAAc,CAACjY,CAAC,CAAC;AAC3B;EAEA,SAASwD,GAAGA,CAACxD,CAAwB,EAAA;AACnCgY,IAAAA,KAAK,IAAIC,cAAc,CAACjY,CAAC,CAAC;AAC5B;EAEA,SAASuR,QAAQA,CAACvR,CAAwB,EAAA;AACxCgY,IAAAA,KAAK,IAAIC,cAAc,CAACjY,CAAC,CAAC;AAC5B;EAEA,SAASiY,cAAcA,CAACjY,CAAwB,EAAA;IAC9C,OAAOV,QAAQ,CAACU,CAAC,CAAC,GAAGA,CAAC,GAAGA,CAAC,CAACwH,GAAG,EAAE;AAClC;AAEA,EAAA,MAAMnE,IAAI,GAAiB;IACzBmE,GAAG;IACHC,GAAG;IACHjE,GAAG;AACH+N,IAAAA;GACD;AACD,EAAA,OAAOlO,IAAI;AACb;;AC9BgB,SAAA6U,SAASA,CACvBtS,IAAc,EACdsI,SAAsB,EAAA;EAEtB,MAAMiK,SAAS,GAAGvS,IAAI,CAACI,MAAM,KAAK,GAAG,GAAGoS,CAAC,GAAGC,CAAC;AAC7C,EAAA,MAAMC,cAAc,GAAGpK,SAAS,CAACqK,KAAK;EACtC,IAAIC,cAAc,GAAkB,IAAI;EACxC,IAAItH,QAAQ,GAAG,KAAK;EAEpB,SAASkH,CAACA,CAACpY,CAAS,EAAA;IAClB,OAAO,CAAA,YAAA,EAAeA,CAAC,CAAa,WAAA,CAAA;AACtC;EAEA,SAASqY,CAACA,CAACrY,CAAS,EAAA;IAClB,OAAO,CAAA,gBAAA,EAAmBA,CAAC,CAAS,OAAA,CAAA;AACtC;EAEA,SAASyY,EAAEA,CAAC5Q,MAAc,EAAA;AACxB,IAAA,IAAIqJ,QAAQ,EAAE;IAEd,MAAMwH,SAAS,GAAGhY,kBAAkB,CAACkF,IAAI,CAACc,SAAS,CAACmB,MAAM,CAAC,CAAC;IAC5D,IAAI6Q,SAAS,KAAKF,cAAc,EAAE;AAElCF,IAAAA,cAAc,CAACK,SAAS,GAAGR,SAAS,CAACO,SAAS,CAAC;AAC/CF,IAAAA,cAAc,GAAGE,SAAS;AAC5B;EAEA,SAASlH,YAAYA,CAACC,MAAe,EAAA;IACnCP,QAAQ,GAAG,CAACO,MAAM;AACpB;EAEA,SAASrN,KAAKA,GAAA;AACZ,IAAA,IAAI8M,QAAQ,EAAE;IACdoH,cAAc,CAACK,SAAS,GAAG,EAAE;AAC7B,IAAA,IAAI,CAACzK,SAAS,CAAC0K,YAAY,CAAC,OAAO,CAAC,EAAE1K,SAAS,CAAC2K,eAAe,CAAC,OAAO,CAAC;AAC1E;AAEA,EAAA,MAAMxV,IAAI,GAAkB;IAC1Be,KAAK;IACLqU,EAAE;AACFjH,IAAAA;GACD;AACD,EAAA,OAAOnO,IAAI;AACb;;SC3BgByV,WAAWA,CACzBlT,IAAc,EACd7C,QAAgB,EAChB4O,WAAmB,EACnBjD,UAAoB,EACpBqK,kBAA4B,EAC5B3E,KAAe,EACfhB,WAAqB,EACrBrL,QAAsB,EACtBoG,MAAqB,EAAA;EAErB,MAAM6K,cAAc,GAAG,GAAG;AAC1B,EAAA,MAAMC,QAAQ,GAAGpY,SAAS,CAACkY,kBAAkB,CAAC;EAC9C,MAAMG,SAAS,GAAGrY,SAAS,CAACkY,kBAAkB,CAAC,CAACI,OAAO,EAAE;EACzD,MAAMC,UAAU,GAAGC,WAAW,EAAE,CAAC9K,MAAM,CAAC+K,SAAS,EAAE,CAAC;AAEpD,EAAA,SAASC,gBAAgBA,CAACC,OAAiB,EAAE7X,IAAY,EAAA;IACvD,OAAO6X,OAAO,CAACrX,MAAM,CAAC,CAACsT,CAAS,EAAE5T,CAAC,KAAI;AACrC,MAAA,OAAO4T,CAAC,GAAGsD,kBAAkB,CAAClX,CAAC,CAAC;KACjC,EAAEF,IAAI,CAAC;AACV;AAEA,EAAA,SAAS8X,WAAWA,CAACD,OAAiB,EAAEE,GAAW,EAAA;IACjD,OAAOF,OAAO,CAACrX,MAAM,CAAC,CAACsT,CAAW,EAAE5T,CAAC,KAAI;AACvC,MAAA,MAAM8X,YAAY,GAAGJ,gBAAgB,CAAC9D,CAAC,EAAEiE,GAAG,CAAC;AAC7C,MAAA,OAAOC,YAAY,GAAG,CAAC,GAAGlE,CAAC,CAAClH,MAAM,CAAC,CAAC1M,CAAC,CAAC,CAAC,GAAG4T,CAAC;KAC5C,EAAE,EAAE,CAAC;AACR;EAEA,SAASmE,eAAeA,CAACjM,MAAc,EAAA;IACrC,OAAOyG,KAAK,CAACpT,GAAG,CAAC,CAACwR,IAAI,EAAEjR,KAAK,MAAM;MACjC0B,KAAK,EAAEuP,IAAI,GAAG9D,UAAU,CAACnN,KAAK,CAAC,GAAGyX,cAAc,GAAGrL,MAAM;AACzDxK,MAAAA,GAAG,EAAEqP,IAAI,GAAGzP,QAAQ,GAAGiW,cAAc,GAAGrL;AACzC,KAAA,CAAC,CAAC;AACL;AAEA,EAAA,SAASkM,cAAcA,CACrBL,OAAiB,EACjB7L,MAAc,EACdmM,SAAkB,EAAA;AAElB,IAAA,MAAMC,WAAW,GAAGH,eAAe,CAACjM,MAAM,CAAC;AAE3C,IAAA,OAAO6L,OAAO,CAACxY,GAAG,CAAEO,KAAK,IAAI;AAC3B,MAAA,MAAMyY,OAAO,GAAGF,SAAS,GAAG,CAAC,GAAG,CAACnI,WAAW;AAC5C,MAAA,MAAMsI,OAAO,GAAGH,SAAS,GAAGnI,WAAW,GAAG,CAAC;AAC3C,MAAA,MAAMuI,SAAS,GAAGJ,SAAS,GAAG,KAAK,GAAG,OAAO;MAC7C,MAAMK,SAAS,GAAGJ,WAAW,CAACxY,KAAK,CAAC,CAAC2Y,SAAS,CAAC;MAE/C,OAAO;QACL3Y,KAAK;QACL4Y,SAAS;AACTC,QAAAA,aAAa,EAAEtC,QAAQ,CAAC,CAAC,CAAC,CAAC;QAC3BK,SAAS,EAAED,SAAS,CAACtS,IAAI,EAAEuI,MAAM,CAAC5M,KAAK,CAAC,CAAC;AACzCsG,QAAAA,MAAM,EAAEA,MAAOE,QAAQ,CAACP,GAAG,EAAE,GAAG2S,SAAS,GAAGH,OAAO,GAAGC;OACvD;AACH,KAAC,CAAC;AACJ;EAEA,SAASZ,WAAWA,GAAA;AAClB,IAAA,MAAMK,GAAG,GAAGtG,WAAW,CAAC,CAAC,CAAC;AAC1B,IAAA,MAAMoG,OAAO,GAAGC,WAAW,CAACP,SAAS,EAAEQ,GAAG,CAAC;AAC3C,IAAA,OAAOG,cAAc,CAACL,OAAO,EAAE7H,WAAW,EAAE,KAAK,CAAC;AACpD;EAEA,SAAS2H,SAASA,GAAA;IAChB,MAAMI,GAAG,GAAG3W,QAAQ,GAAGqQ,WAAW,CAAC,CAAC,CAAC,GAAG,CAAC;AACzC,IAAA,MAAMoG,OAAO,GAAGC,WAAW,CAACR,QAAQ,EAAES,GAAG,CAAC;IAC1C,OAAOG,cAAc,CAACL,OAAO,EAAE,CAAC7H,WAAW,EAAE,IAAI,CAAC;AACpD;EAEA,SAAS0I,OAAOA,GAAA;AACd,IAAA,OAAOjB,UAAU,CAACkB,KAAK,CAAC,CAAC;AAAE/Y,MAAAA;AAAO,KAAA,KAAI;MACpC,MAAMgZ,YAAY,GAAGtB,QAAQ,CAAC5U,MAAM,CAAExC,CAAC,IAAKA,CAAC,KAAKN,KAAK,CAAC;AACxD,MAAA,OAAOgY,gBAAgB,CAACgB,YAAY,EAAExX,QAAQ,CAAC,IAAI,GAAG;AACxD,KAAC,CAAC;AACJ;EAEA,SAASqE,IAAIA,GAAA;AACXgS,IAAAA,UAAU,CAAC9W,OAAO,CAAE6X,SAAS,IAAI;MAC/B,MAAM;QAAEtS,MAAM;QAAEsQ,SAAS;AAAEiC,QAAAA;AAAa,OAAE,GAAGD,SAAS;AACtD,MAAA,MAAMK,aAAa,GAAG3S,MAAM,EAAE;AAC9B,MAAA,IAAI2S,aAAa,KAAKJ,aAAa,CAAC5S,GAAG,EAAE,EAAE;AAC3C2Q,MAAAA,SAAS,CAACM,EAAE,CAAC+B,aAAa,CAAC;AAC3BJ,MAAAA,aAAa,CAAC3S,GAAG,CAAC+S,aAAa,CAAC;AAClC,KAAC,CAAC;AACJ;EAEA,SAASpW,KAAKA,GAAA;AACZgV,IAAAA,UAAU,CAAC9W,OAAO,CAAE6X,SAAS,IAAKA,SAAS,CAAChC,SAAS,CAAC/T,KAAK,EAAE,CAAC;AAChE;AAEA,EAAA,MAAMf,IAAI,GAAoB;IAC5BgX,OAAO;IACPjW,KAAK;IACLgD,IAAI;AACJgS,IAAAA;GACD;AACD,EAAA,OAAO/V,IAAI;AACb;;SC5GgBoX,aAAaA,CAC3BvM,SAAsB,EACtB9F,YAA8B,EAC9BsS,WAAoC,EAAA;AAEpC,EAAA,IAAIC,gBAAkC;EACtC,IAAIhM,SAAS,GAAG,KAAK;EAErB,SAAS3J,IAAIA,CAAC6E,QAA2B,EAAA;IACvC,IAAI,CAAC6Q,WAAW,EAAE;IAElB,SAAS7L,eAAeA,CAAC+L,SAA2B,EAAA;AAClD,MAAA,KAAK,MAAMC,QAAQ,IAAID,SAAS,EAAE;AAChC,QAAA,IAAIC,QAAQ,CAACnX,IAAI,KAAK,WAAW,EAAE;UACjCmG,QAAQ,CAACyF,MAAM,EAAE;AACjBlH,UAAAA,YAAY,CAACsD,IAAI,CAAC,eAAe,CAAC;AAClC,UAAA;AACF;AACF;AACF;AAEAiP,IAAAA,gBAAgB,GAAG,IAAIG,gBAAgB,CAAEF,SAAS,IAAI;AACpD,MAAA,IAAIjM,SAAS,EAAE;MACf,IAAIlP,SAAS,CAACib,WAAW,CAAC,IAAIA,WAAW,CAAC7Q,QAAQ,EAAE+Q,SAAS,CAAC,EAAE;QAC9D/L,eAAe,CAAC+L,SAAS,CAAC;AAC5B;AACF,KAAC,CAAC;AAEFD,IAAAA,gBAAgB,CAACnL,OAAO,CAACtB,SAAS,EAAE;AAAE6M,MAAAA,SAAS,EAAE;AAAM,KAAA,CAAC;AAC1D;EAEA,SAAS5V,OAAOA,GAAA;AACd,IAAA,IAAIwV,gBAAgB,EAAEA,gBAAgB,CAAClL,UAAU,EAAE;AACnDd,IAAAA,SAAS,GAAG,IAAI;AAClB;AAEA,EAAA,MAAMtL,IAAI,GAAsB;IAC9B2B,IAAI;AACJG,IAAAA;GACD;AACD,EAAA,OAAO9B,IAAI;AACb;;AC1CM,SAAU2X,YAAYA,CAC1B9M,SAAsB,EACtBC,MAAqB,EACrB/F,YAA8B,EAC9B6S,SAAkC,EAAA;EAElC,MAAMC,oBAAoB,GAA6B,EAAE;EACzD,IAAIC,WAAW,GAAoB,IAAI;EACvC,IAAIC,cAAc,GAAoB,IAAI;AAC1C,EAAA,IAAIC,oBAA0C;EAC9C,IAAI1M,SAAS,GAAG,KAAK;EAErB,SAAS3J,IAAIA,GAAA;AACXqW,IAAAA,oBAAoB,GAAG,IAAIC,oBAAoB,CAC5CxM,OAAO,IAAI;AACV,MAAA,IAAIH,SAAS,EAAE;AAEfG,MAAAA,OAAO,CAACxM,OAAO,CAAEyM,KAAK,IAAI;QACxB,MAAMxN,KAAK,GAAG4M,MAAM,CAACe,OAAO,CAAcH,KAAK,CAAClH,MAAM,CAAC;AACvDqT,QAAAA,oBAAoB,CAAC3Z,KAAK,CAAC,GAAGwN,KAAK;AACrC,OAAC,CAAC;AAEFoM,MAAAA,WAAW,GAAG,IAAI;AAClBC,MAAAA,cAAc,GAAG,IAAI;AACrBhT,MAAAA,YAAY,CAACsD,IAAI,CAAC,cAAc,CAAC;AACnC,KAAC,EACD;MACEoL,IAAI,EAAE5I,SAAS,CAACqN,aAAa;AAC7BN,MAAAA;AACD,KAAA,CACF;IAED9M,MAAM,CAAC7L,OAAO,CAAEqV,KAAK,IAAK0D,oBAAoB,CAAC7L,OAAO,CAACmI,KAAK,CAAC,CAAC;AAChE;EAEA,SAASxS,OAAOA,GAAA;AACd,IAAA,IAAIkW,oBAAoB,EAAEA,oBAAoB,CAAC5L,UAAU,EAAE;AAC3Dd,IAAAA,SAAS,GAAG,IAAI;AAClB;EAEA,SAAS6M,gBAAgBA,CAACC,MAAe,EAAA;IACvC,OAAO1a,UAAU,CAACma,oBAAoB,CAAC,CAAC/Y,MAAM,CAC5C,CAACuZ,IAAc,EAAEzM,UAAU,KAAI;AAC7B,MAAA,MAAM1N,KAAK,GAAGoa,QAAQ,CAAC1M,UAAU,CAAC;MAClC,MAAM;AAAE2M,QAAAA;AAAgB,OAAA,GAAGV,oBAAoB,CAAC3Z,KAAK,CAAC;AACtD,MAAA,MAAMsa,WAAW,GAAGJ,MAAM,IAAIG,cAAc;AAC5C,MAAA,MAAME,cAAc,GAAG,CAACL,MAAM,IAAI,CAACG,cAAc;MAEjD,IAAIC,WAAW,IAAIC,cAAc,EAAEJ,IAAI,CAACvX,IAAI,CAAC5C,KAAK,CAAC;AACnD,MAAA,OAAOma,IAAI;KACZ,EACD,EAAE,CACH;AACH;AAEA,EAAA,SAASlU,GAAGA,CAACiU,MAAA,GAAkB,IAAI,EAAA;AACjC,IAAA,IAAIA,MAAM,IAAIN,WAAW,EAAE,OAAOA,WAAW;AAC7C,IAAA,IAAI,CAACM,MAAM,IAAIL,cAAc,EAAE,OAAOA,cAAc;AAEpD,IAAA,MAAMxG,YAAY,GAAG4G,gBAAgB,CAACC,MAAM,CAAC;AAE7C,IAAA,IAAIA,MAAM,EAAEN,WAAW,GAAGvG,YAAY;AACtC,IAAA,IAAI,CAAC6G,MAAM,EAAEL,cAAc,GAAGxG,YAAY;AAE1C,IAAA,OAAOA,YAAY;AACrB;AAEA,EAAA,MAAMvR,IAAI,GAAqB;IAC7B2B,IAAI;IACJG,OAAO;AACPqC,IAAAA;GACD;AAED,EAAA,OAAOnE,IAAI;AACb;;AC9EgB,SAAA0Y,UAAUA,CACxBnW,IAAc,EACdkO,aAA2B,EAC3BC,UAA0B,EAC1B5F,MAAqB,EACrB6N,WAAoB,EACpBrZ,WAAuB,EAAA;EAEvB,MAAM;IAAE2D,WAAW;IAAEJ,SAAS;AAAEE,IAAAA;AAAO,GAAE,GAAGR,IAAI;AAChD,EAAA,MAAMqW,WAAW,GAAGlI,UAAU,CAAC,CAAC,CAAC,IAAIiI,WAAW;AAChD,EAAA,MAAME,QAAQ,GAAGC,eAAe,EAAE;AAClC,EAAA,MAAMC,MAAM,GAAGC,aAAa,EAAE;AAC9B,EAAA,MAAM3N,UAAU,GAAGqF,UAAU,CAAC/S,GAAG,CAACsF,WAAW,CAAC;AAC9C,EAAA,MAAMyS,kBAAkB,GAAGuD,eAAe,EAAE;EAE5C,SAASH,eAAeA,GAAA;AACtB,IAAA,IAAI,CAACF,WAAW,EAAE,OAAO,CAAC;AAC1B,IAAA,MAAMM,SAAS,GAAGxI,UAAU,CAAC,CAAC,CAAC;IAC/B,OAAOhU,OAAO,CAAC+T,aAAa,CAAC5N,SAAS,CAAC,GAAGqW,SAAS,CAACrW,SAAS,CAAC,CAAC;AACjE;EAEA,SAASmW,aAAaA,GAAA;AACpB,IAAA,IAAI,CAACJ,WAAW,EAAE,OAAO,CAAC;IAC1B,MAAM1D,KAAK,GAAG5V,WAAW,CAAC6Z,gBAAgB,CAACtb,SAAS,CAACiN,MAAM,CAAC,CAAC;IAC7D,OAAO6E,UAAU,CAACuF,KAAK,CAACkE,gBAAgB,CAAC,CAAUrW,OAAAA,EAAAA,OAAO,CAAE,CAAA,CAAC,CAAC;AAChE;EAEA,SAASkW,eAAeA,GAAA;IACtB,OAAOvI,UAAU,CACd/S,GAAG,CAAC,CAACwT,IAAI,EAAEjT,KAAK,EAAEgT,KAAK,KAAI;MAC1B,MAAM1B,OAAO,GAAG,CAACtR,KAAK;AACtB,MAAA,MAAMuR,MAAM,GAAGxR,gBAAgB,CAACiT,KAAK,EAAEhT,KAAK,CAAC;MAC7C,IAAIsR,OAAO,EAAE,OAAOnE,UAAU,CAACnN,KAAK,CAAC,GAAG2a,QAAQ;MAChD,IAAIpJ,MAAM,EAAE,OAAOpE,UAAU,CAACnN,KAAK,CAAC,GAAG6a,MAAM;AAC7C,MAAA,OAAO7H,KAAK,CAAChT,KAAK,GAAG,CAAC,CAAC,CAAC2E,SAAS,CAAC,GAAGsO,IAAI,CAACtO,SAAS,CAAC;AACtD,KAAC,CAAC,CACDlF,GAAG,CAACjB,OAAO,CAAC;AACjB;AAEA,EAAA,MAAMsD,IAAI,GAAmB;IAC3BqL,UAAU;IACVqK,kBAAkB;IAClBmD,QAAQ;AACRE,IAAAA;GACD;AACD,EAAA,OAAO/Y,IAAI;AACb;;SCzCgBqZ,cAAcA,CAC5B9W,IAAc,EACd7C,QAAgB,EAChBiR,cAAwC,EACxC5M,IAAa,EACb0M,aAA2B,EAC3BC,UAA0B,EAC1BmI,QAAgB,EAChBE,MAAc,EACdtK,cAAsB,EAAA;EAEtB,MAAM;IAAE5L,SAAS;IAAEE,OAAO;AAAEM,IAAAA;AAAS,GAAE,GAAGd,IAAI;AAC9C,EAAA,MAAM+W,aAAa,GAAGrd,QAAQ,CAAC0U,cAAc,CAAC;AAE9C,EAAA,SAAS4I,QAAQA,CAAO9b,KAAa,EAAE+b,SAAiB,EAAA;AACtD,IAAA,OAAOhc,SAAS,CAACC,KAAK,CAAC,CACpBuD,MAAM,CAAExC,CAAC,IAAKA,CAAC,GAAGgb,SAAS,KAAK,CAAC,CAAC,CAClC7b,GAAG,CAAEa,CAAC,IAAKf,KAAK,CAACoS,KAAK,CAACrR,CAAC,EAAEA,CAAC,GAAGgb,SAAS,CAAC,CAAC;AAC9C;EAEA,SAASC,MAAMA,CAAOhc,KAAa,EAAA;AACjC,IAAA,IAAI,CAACA,KAAK,CAACO,MAAM,EAAE,OAAO,EAAE;AAE5B,IAAA,OAAOR,SAAS,CAACC,KAAK,CAAC,CACpBqB,MAAM,CAAC,CAAC+S,MAAgB,EAAE6H,KAAK,EAAExb,KAAK,KAAI;AACzC,MAAA,MAAMyb,KAAK,GAAG9b,SAAS,CAACgU,MAAM,CAAC,IAAI,CAAC;AACpC,MAAA,MAAMrC,OAAO,GAAGmK,KAAK,KAAK,CAAC;AAC3B,MAAA,MAAMlK,MAAM,GAAGiK,KAAK,KAAK5b,cAAc,CAACL,KAAK,CAAC;AAE9C,MAAA,MAAMmc,KAAK,GAAGnJ,aAAa,CAAC5N,SAAS,CAAC,GAAG6N,UAAU,CAACiJ,KAAK,CAAC,CAAC9W,SAAS,CAAC;AACrE,MAAA,MAAMgX,KAAK,GAAGpJ,aAAa,CAAC5N,SAAS,CAAC,GAAG6N,UAAU,CAACgJ,KAAK,CAAC,CAAC3W,OAAO,CAAC;AACnE,MAAA,MAAM+W,IAAI,GAAG,CAAC/V,IAAI,IAAIyL,OAAO,GAAGnM,SAAS,CAACwV,QAAQ,CAAC,GAAG,CAAC;AACvD,MAAA,MAAMkB,IAAI,GAAG,CAAChW,IAAI,IAAI0L,MAAM,GAAGpM,SAAS,CAAC0V,MAAM,CAAC,GAAG,CAAC;AACpD,MAAA,MAAMiB,SAAS,GAAGtd,OAAO,CAACmd,KAAK,GAAGE,IAAI,IAAIH,KAAK,GAAGE,IAAI,CAAC,CAAC;AAExD,MAAA,IAAI5b,KAAK,IAAI8b,SAAS,GAAGta,QAAQ,GAAG+O,cAAc,EAAEoD,MAAM,CAAC/Q,IAAI,CAAC4Y,KAAK,CAAC;MACtE,IAAIjK,MAAM,EAAEoC,MAAM,CAAC/Q,IAAI,CAACrD,KAAK,CAACO,MAAM,CAAC;AACrC,MAAA,OAAO6T,MAAM;AACf,KAAC,EAAE,EAAE,CAAC,CACLlU,GAAG,CAAC,CAACsc,WAAW,EAAE/b,KAAK,EAAE2T,MAAM,KAAI;AAClC,MAAA,MAAMqI,YAAY,GAAGtd,IAAI,CAACmB,GAAG,CAAC8T,MAAM,CAAC3T,KAAK,GAAG,CAAC,CAAC,IAAI,CAAC,CAAC;AACrD,MAAA,OAAOT,KAAK,CAACoS,KAAK,CAACqK,YAAY,EAAED,WAAW,CAAC;AAC/C,KAAC,CAAC;AACN;EAEA,SAASrJ,WAAWA,CAAOnT,KAAa,EAAA;AACtC,IAAA,OAAO6b,aAAa,GAAGC,QAAQ,CAAC9b,KAAK,EAAEkT,cAAc,CAAC,GAAG8I,MAAM,CAAChc,KAAK,CAAC;AACxE;AAEA,EAAA,MAAMuC,IAAI,GAAuB;AAC/B4Q,IAAAA;GACD;AACD,EAAA,OAAO5Q,IAAI;AACb;;ACOgB,SAAAma,MAAMA,CACpB1G,IAAiB,EACjB5I,SAAsB,EACtBC,MAAqB,EACrB3J,aAAuB,EACvB7B,WAAuB,EACvBiB,OAAoB,EACpBwE,YAA8B,EAAA;AAE9B;EACA,MAAM;IACJtF,KAAK;AACL8C,IAAAA,IAAI,EAAE6X,UAAU;IAChB/W,SAAS;IACTgX,UAAU;IACVtW,IAAI;IACJqJ,QAAQ;IACRnI,QAAQ;IACRC,aAAa;IACboV,eAAe;AACf3J,IAAAA,cAAc,EAAEC,WAAW;IAC3BzL,SAAS;IACTqJ,aAAa;IACbzD,WAAW;IACXsM,WAAW;IACXhS,SAAS;AACTsO,IAAAA;AACD,GAAA,GAAGpT,OAAO;AAEX;EACA,MAAMkO,cAAc,GAAG,CAAC;AACxB,EAAA,MAAMzD,SAAS,GAAGf,SAAS,EAAE;AAC7B,EAAA,MAAMwG,aAAa,GAAGzF,SAAS,CAACjL,OAAO,CAAC8K,SAAS,CAAC;EAClD,MAAM6F,UAAU,GAAG5F,MAAM,CAACnN,GAAG,CAACqN,SAAS,CAACjL,OAAO,CAAC;AAChD,EAAA,MAAMwC,IAAI,GAAGD,IAAI,CAAC8X,UAAU,EAAE/W,SAAS,CAAC;AACxC,EAAA,MAAM3D,QAAQ,GAAG6C,IAAI,CAACU,WAAW,CAACwN,aAAa,CAAC;AAChD,EAAA,MAAMzL,aAAa,GAAG2F,aAAa,CAACjL,QAAQ,CAAC;AAC7C,EAAA,MAAM8Q,SAAS,GAAGhR,SAAS,CAACC,KAAK,EAAEC,QAAQ,CAAC;AAC5C,EAAA,MAAM4R,YAAY,GAAG,CAACvN,IAAI,IAAI,CAAC,CAACyK,aAAa;AAC7C,EAAA,MAAMmK,WAAW,GAAG5U,IAAI,IAAI,CAAC,CAACyK,aAAa;EAC3C,MAAM;IAAEnD,UAAU;IAAEqK,kBAAkB;IAAEmD,QAAQ;AAAEE,IAAAA;AAAQ,GAAA,GAAGL,UAAU,CACrEnW,IAAI,EACJkO,aAAa,EACbC,UAAU,EACV5F,MAAM,EACN6N,WAAW,EACXrZ,WAAW,CACZ;EACD,MAAMqR,cAAc,GAAG0I,cAAc,CACnC9W,IAAI,EACJ7C,QAAQ,EACRkR,WAAW,EACX7M,IAAI,EACJ0M,aAAa,EACbC,UAAU,EACVmI,QAAQ,EACRE,MAAM,EACNtK,cAAc,CACf;EACD,MAAM;IAAEsC,KAAK;AAAExC,IAAAA;AAAc,GAAA,GAAGgC,WAAW,CACzChO,IAAI,EACJiO,SAAS,EACTC,aAAa,EACbC,UAAU,EACVC,cAAc,CACf;EACD,MAAMrC,WAAW,GAAG,CAACzQ,SAAS,CAACkT,KAAK,CAAC,GAAGlT,SAAS,CAAC6X,kBAAkB,CAAC;EACrE,MAAM;IAAE3G,cAAc;AAAEF,IAAAA;AAAoB,GAAA,GAAGR,aAAa,CAC1D3O,QAAQ,EACR4O,WAAW,EACXC,YAAY,EACZC,aAAa,EACbC,cAAc,CACf;AACD,EAAA,MAAMsB,WAAW,GAAGuB,YAAY,GAAGvC,cAAc,GAAGR,YAAY;EAChE,MAAM;AAAEd,IAAAA;GAAO,GAAGqC,WAAW,CAACxB,WAAW,EAAEyB,WAAW,EAAEhM,IAAI,CAAC;AAE7D;AACA,EAAA,MAAM7F,KAAK,GAAG4F,OAAO,CAAChG,cAAc,CAACiS,WAAW,CAAC,EAAEsK,UAAU,EAAEtW,IAAI,CAAC;AACpE,EAAA,MAAMqP,aAAa,GAAGlV,KAAK,CAACmG,KAAK,EAAE;AACnC,EAAA,MAAMkN,YAAY,GAAG/T,SAAS,CAACsN,MAAM,CAAC;AAEtC;EACA,MAAM1J,MAAM,GAAyBA,CAAC;IACpCmZ,WAAW;IACX1V,UAAU;IACV6J,YAAY;AACZnO,IAAAA,OAAO,EAAE;AAAEwD,MAAAA;AAAM;AAAA,GAClB,KAAI;AACH,IAAA,IAAI,CAACA,IAAI,EAAE2K,YAAY,CAAC/K,SAAS,CAAC4W,WAAW,CAACtS,WAAW,EAAE,CAAC;IAC5DpD,UAAU,CAACkI,IAAI,EAAE;GAClB;EAED,MAAM1L,MAAM,GAAyBA,CACnC;IACEwD,UAAU;IACViQ,SAAS;IACTpQ,QAAQ;IACR4H,cAAc;IACdC,gBAAgB;IAChBiO,YAAY;IACZC,WAAW;IACXF,WAAW;IACX5V,SAAS;IACTI,YAAY;IACZ2J,YAAY;AACZnO,IAAAA,OAAO,EAAE;AAAEwD,MAAAA;AAAM;GAClB,EACD5B,KAAK,KACH;AACF,IAAA,MAAMuY,YAAY,GAAG7V,UAAU,CAACsI,OAAO,EAAE;AACzC,IAAA,MAAMwN,YAAY,GAAG,CAACjM,YAAY,CAACZ,eAAe,EAAE;IACpD,MAAM8M,UAAU,GAAG7W,IAAI,GAAG2W,YAAY,GAAGA,YAAY,IAAIC,YAAY;IAErE,IAAIC,UAAU,IAAI,CAACL,WAAW,CAACtS,WAAW,EAAE,EAAE;MAC5CtD,SAAS,CAAC5C,IAAI,EAAE;AAChBgD,MAAAA,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;AAC7B;IACA,IAAI,CAACuS,UAAU,EAAE7V,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;AAE5C,IAAA,MAAMwS,oBAAoB,GACxBnW,QAAQ,CAACP,GAAG,EAAE,GAAGhC,KAAK,GAAGoK,gBAAgB,CAACpI,GAAG,EAAE,IAAI,CAAC,GAAGhC,KAAK,CAAC;AAE/DmK,IAAAA,cAAc,CAAClI,GAAG,CAACyW,oBAAoB,CAAC;AAExC,IAAA,IAAI9W,IAAI,EAAE;MACRyW,YAAY,CAACzW,IAAI,CAACc,UAAU,CAACxB,SAAS,EAAE,CAAC;MACzCoX,WAAW,CAAC1W,IAAI,EAAE;AACpB;IAEA+Q,SAAS,CAACM,EAAE,CAAC9I,cAAc,CAACnI,GAAG,EAAE,CAAC;GACnC;EAED,MAAMQ,SAAS,GAAGzD,UAAU,CAC1BC,aAAa,EACb7B,WAAW,EACX,MAAM8B,MAAM,CAAC0Z,MAAM,CAAC,EACnB3Y,KAAa,IAAKd,MAAM,CAACyZ,MAAM,EAAE3Y,KAAK,CAAC,CACzC;AAED;EACA,MAAMgH,QAAQ,GAAG,IAAI;EACrB,MAAM4R,aAAa,GAAGhL,WAAW,CAAC7R,KAAK,CAACiG,GAAG,EAAE,CAAC;AAC9C,EAAA,MAAMO,QAAQ,GAAG+P,QAAQ,CAACsG,aAAa,CAAC;AACxC,EAAA,MAAMxO,gBAAgB,GAAGkI,QAAQ,CAACsG,aAAa,CAAC;AAChD,EAAA,MAAMzO,cAAc,GAAGmI,QAAQ,CAACsG,aAAa,CAAC;AAC9C,EAAA,MAAMvW,MAAM,GAAGiQ,QAAQ,CAACsG,aAAa,CAAC;AACtC,EAAA,MAAMlW,UAAU,GAAGwH,UAAU,CAC3B3H,QAAQ,EACR4H,cAAc,EACdC,gBAAgB,EAChB/H,MAAM,EACN4I,QAAQ,EACRjE,QAAQ,CACT;AACD,EAAA,MAAMrE,YAAY,GAAGiN,YAAY,CAC/BhO,IAAI,EACJgM,WAAW,EACXzB,WAAW,EACXb,KAAK,EACLjJ,MAAM,CACP;AACD,EAAA,MAAMI,QAAQ,GAAGsO,QAAQ,CACvBvO,SAAS,EACTzG,KAAK,EACLkV,aAAa,EACbvO,UAAU,EACVC,YAAY,EACZN,MAAM,EACNO,YAAY,CACb;AACD,EAAA,MAAMiW,cAAc,GAAG1K,cAAc,CAAC7C,KAAK,CAAC;AAC5C,EAAA,MAAMiG,UAAU,GAAGzT,UAAU,EAAE;EAC/B,MAAMgb,YAAY,GAAGtD,YAAY,CAC/B9M,SAAS,EACTC,MAAM,EACN/F,YAAY,EACZuV,eAAe,CAChB;EACD,MAAM;AAAE9I,IAAAA;AAAa,GAAE,GAAGH,aAAa,CACrCC,YAAY,EACZ9C,aAAa,EACbuB,WAAW,EACXlB,kBAAkB,EAClB8B,cAAc,EACdY,YAAY,CACb;AACD,EAAA,MAAM2J,UAAU,GAAG1H,UAAU,CAC3BC,IAAI,EACJ3I,MAAM,EACN0G,aAAa,EACb5M,QAAQ,EACRC,UAAU,EACV6O,UAAU,EACV3O,YAAY,EACZ4O,UAAU,CACX;AAED;AACA,EAAA,MAAMmH,MAAM,GAAe;IACzB3Z,aAAa;IACb7B,WAAW;IACXyF,YAAY;IACZ0L,aAAa;IACbC,UAAU;IACV/L,SAAS;IACTpC,IAAI;IACJgY,WAAW,EAAEjW,WAAW,CACtB/B,IAAI,EACJkR,IAAI,EACJtS,aAAa,EACb7B,WAAW,EACXkF,MAAM,EACN6E,WAAW,CAAC9G,IAAI,EAAEjD,WAAW,CAAC,EAC9BoF,QAAQ,EACRC,SAAS,EACTC,QAAQ,EACRC,UAAU,EACVC,YAAY,EACZ5G,KAAK,EACL6G,YAAY,EACZC,aAAa,EACbC,QAAQ,EACRC,aAAa,EACbC,SAAS,EACTgE,QAAQ,EACR9D,SAAS,CACV;IACDqO,UAAU;IACV1O,aAAa;IACb9G,KAAK;IACLkV,aAAa;IACb3F,KAAK;IACL/I,QAAQ;IACR4H,cAAc;IACdC,gBAAgB;IAChBhM,OAAO;AACP4a,IAAAA,aAAa,EAAEvQ,aAAa,CAC1BC,SAAS,EACT9F,YAAY,EACZzF,WAAW,EACXwL,MAAM,EACNvI,IAAI,EACJwI,WAAW,EACXC,SAAS,CACV;IACDnG,UAAU;AACV6J,IAAAA,YAAY,EAAElB,YAAY,CACxBC,KAAK,EACLnB,cAAc,EACd9H,MAAM,EACNK,UAAU,EACVG,aAAa,CACd;AACDwV,IAAAA,YAAY,EAAExK,YAAY,CAAC1B,WAAW,EAAEb,KAAK,EAAEnB,cAAc,EAAE,CAC7D5H,QAAQ,EACR4H,cAAc,EACdC,gBAAgB,EAChB/H,MAAM,CACP,CAAC;IACFwW,cAAc;IACdI,cAAc,EAAErL,WAAW,CAACpS,GAAG,CAACqd,cAAc,CAAC7W,GAAG,CAAC;IACnD4L,WAAW;IACXjL,YAAY;IACZF,QAAQ;IACR6V,WAAW,EAAEhF,WAAW,CACtBlT,IAAI,EACJ7C,QAAQ,EACR4O,WAAW,EACXjD,UAAU,EACVqK,kBAAkB,EAClB3E,KAAK,EACLhB,WAAW,EACXzD,cAAc,EACdxB,MAAM,CACP;IACDoQ,UAAU;IACVG,aAAa,EAAEjE,aAAa,CAACvM,SAAS,EAAE9F,YAAY,EAAEsS,WAAW,CAAC;IAClE4D,YAAY;IACZ1J,YAAY;IACZC,aAAa;IACbb,cAAc;IACdnM,MAAM;AACNsQ,IAAAA,SAAS,EAAED,SAAS,CAACtS,IAAI,EAAEsI,SAAS;GACrC;AAED,EAAA,OAAOiQ,MAAM;AACf;;SC5UgBQ,YAAYA,GAAA;EAC1B,IAAIpb,SAAS,GAAkB,EAAE;AACjC,EAAA,IAAIqb,GAAsB;EAE1B,SAAS5Z,IAAIA,CAAC6E,QAA2B,EAAA;AACvC+U,IAAAA,GAAG,GAAG/U,QAAQ;AAChB;EAEA,SAASgV,YAAYA,CAACnc,GAAmB,EAAA;AACvC,IAAA,OAAOa,SAAS,CAACb,GAAG,CAAC,IAAI,EAAE;AAC7B;EAEA,SAASgJ,IAAIA,CAAChJ,GAAmB,EAAA;AAC/Bmc,IAAAA,YAAY,CAACnc,GAAG,CAAC,CAACJ,OAAO,CAAEwc,CAAC,IAAKA,CAAC,CAACF,GAAG,EAAElc,GAAG,CAAC,CAAC;AAC7C,IAAA,OAAOW,IAAI;AACb;AAEA,EAAA,SAAS0b,EAAEA,CAACrc,GAAmB,EAAEsc,EAAgB,EAAA;AAC/Czb,IAAAA,SAAS,CAACb,GAAG,CAAC,GAAGmc,YAAY,CAACnc,GAAG,CAAC,CAAC6L,MAAM,CAAC,CAACyQ,EAAE,CAAC,CAAC;AAC/C,IAAA,OAAO3b,IAAI;AACb;AAEA,EAAA,SAAS4b,GAAGA,CAACvc,GAAmB,EAAEsc,EAAgB,EAAA;AAChDzb,IAAAA,SAAS,CAACb,GAAG,CAAC,GAAGmc,YAAY,CAACnc,GAAG,CAAC,CAAC2B,MAAM,CAAEya,CAAC,IAAKA,CAAC,KAAKE,EAAE,CAAC;AAC1D,IAAA,OAAO3b,IAAI;AACb;EAEA,SAASe,KAAKA,GAAA;IACZb,SAAS,GAAG,EAAE;AAChB;AAEA,EAAA,MAAMF,IAAI,GAAqB;IAC7B2B,IAAI;IACJ0G,IAAI;IACJuT,GAAG;IACHF,EAAE;AACF3a,IAAAA;GACD;AACD,EAAA,OAAOf,IAAI;AACb;;AC5BO,MAAM6b,cAAc,GAAgB;AACzCpc,EAAAA,KAAK,EAAE,QAAQ;AACf8C,EAAAA,IAAI,EAAE,GAAG;AACTsI,EAAAA,SAAS,EAAE,IAAI;AACfC,EAAAA,MAAM,EAAE,IAAI;AACZ0D,EAAAA,aAAa,EAAE,WAAW;AAC1BnL,EAAAA,SAAS,EAAE,KAAK;AAChBsN,EAAAA,cAAc,EAAE,CAAC;AACjB2J,EAAAA,eAAe,EAAE,CAAC;EAClBwB,WAAW,EAAE,EAAE;AACf7W,EAAAA,QAAQ,EAAE,KAAK;AACfC,EAAAA,aAAa,EAAE,EAAE;AACjBnB,EAAAA,IAAI,EAAE,KAAK;AACXoB,EAAAA,SAAS,EAAE,KAAK;AAChBiI,EAAAA,QAAQ,EAAE,EAAE;AACZiN,EAAAA,UAAU,EAAE,CAAC;AACbjM,EAAAA,MAAM,EAAE,IAAI;AACZ/I,EAAAA,SAAS,EAAE,IAAI;AACf0F,EAAAA,WAAW,EAAE,IAAI;AACjBsM,EAAAA,WAAW,EAAE,IAAI;AACjB1D,EAAAA,UAAU,EAAE;CACb;;ACjDK,SAAUoI,cAAcA,CAACzc,WAAuB,EAAA;AACpD,EAAA,SAAS0c,YAAYA,CACnBC,QAAe,EACfC,QAAgB,EAAA;IAEhB,OAAcvd,gBAAgB,CAACsd,QAAQ,EAAEC,QAAQ,IAAI,EAAE,CAAC;AAC1D;EAEA,SAASC,cAAcA,CAA2B5b,OAAa,EAAA;AAC7D,IAAA,MAAM4b,cAAc,GAAG5b,OAAO,CAACub,WAAW,IAAI,EAAE;IAChD,MAAMM,mBAAmB,GAAG1e,UAAU,CAACye,cAAc,CAAC,CACnDnb,MAAM,CAAEqb,KAAK,IAAK/c,WAAW,CAACgd,UAAU,CAACD,KAAK,CAAC,CAACE,OAAO,CAAC,CACxD5e,GAAG,CAAE0e,KAAK,IAAKF,cAAc,CAACE,KAAK,CAAC,CAAC,CACrCvd,MAAM,CAAC,CAACsT,CAAC,EAAEoK,WAAW,KAAKR,YAAY,CAAC5J,CAAC,EAAEoK,WAAW,CAAC,EAAE,EAAE,CAAC;AAE/D,IAAA,OAAOR,YAAY,CAACzb,OAAO,EAAE6b,mBAAmB,CAAC;AACnD;EAEA,SAASK,mBAAmBA,CAACC,WAA0B,EAAA;AACrD,IAAA,OAAOA,WAAW,CACf/e,GAAG,CAAE4C,OAAO,IAAK7C,UAAU,CAAC6C,OAAO,CAACub,WAAW,IAAI,EAAE,CAAC,CAAC,CACvDhd,MAAM,CAAC,CAAC6d,GAAG,EAAEC,YAAY,KAAKD,GAAG,CAACzR,MAAM,CAAC0R,YAAY,CAAC,EAAE,EAAE,CAAC,CAC3Djf,GAAG,CAAC2B,WAAW,CAACgd,UAAU,CAAC;AAChC;AAEA,EAAA,MAAMtc,IAAI,GAAuB;IAC/Bgc,YAAY;IACZG,cAAc;AACdM,IAAAA;GACD;AACD,EAAA,OAAOzc,IAAI;AACb;;ACjCM,SAAU6c,cAAcA,CAC5BC,cAAkC,EAAA;EAElC,IAAIC,aAAa,GAAsB,EAAE;AAEzC,EAAA,SAASpb,IAAIA,CACX6E,QAA2B,EAC3BwW,OAA0B,EAAA;AAE1BD,IAAAA,aAAa,GAAGC,OAAO,CAAChc,MAAM,CAC5B,CAAC;AAAET,MAAAA;KAAS,KAAKuc,cAAc,CAACX,cAAc,CAAC5b,OAAO,CAAC,CAAC6N,MAAM,KAAK,KAAK,CACzE;AACD2O,IAAAA,aAAa,CAAC9d,OAAO,CAAEge,MAAM,IAAKA,MAAM,CAACtb,IAAI,CAAC6E,QAAQ,EAAEsW,cAAc,CAAC,CAAC;AAExE,IAAA,OAAOE,OAAO,CAACle,MAAM,CACnB,CAACnB,GAAG,EAAEsf,MAAM,KAAK3gB,MAAM,CAAC4gB,MAAM,CAACvf,GAAG,EAAE;MAAE,CAACsf,MAAM,CAACE,IAAI,GAAGF;AAAQ,KAAA,CAAC,EAC9D,EAAE,CACH;AACH;EAEA,SAASnb,OAAOA,GAAA;AACdib,IAAAA,aAAa,GAAGA,aAAa,CAAC/b,MAAM,CAAEic,MAAM,IAAKA,MAAM,CAACnb,OAAO,EAAE,CAAC;AACpE;AAEA,EAAA,MAAM9B,IAAI,GAAuB;IAC/B2B,IAAI;AACJG,IAAAA;GACD;AACD,EAAA,OAAO9B,IAAI;AACb;;ACRA,SAASod,aAAaA,CACpB3J,IAAiB,EACjB4J,WAA8B,EAC9BC,WAA+B,EAAA;AAE/B,EAAA,MAAMnc,aAAa,GAAGsS,IAAI,CAACtS,aAAa;AACxC,EAAA,MAAM7B,WAAW,GAAe6B,aAAa,CAACoc,WAAW;AACzD,EAAA,MAAMT,cAAc,GAAGf,cAAc,CAACzc,WAAW,CAAC;AAClD,EAAA,MAAMke,cAAc,GAAGX,cAAc,CAACC,cAAc,CAAC;AACrD,EAAA,MAAMW,aAAa,GAAGxd,UAAU,EAAE;AAClC,EAAA,MAAM8E,YAAY,GAAGuW,YAAY,EAAE;EACnC,MAAM;IAAEU,YAAY;IAAEG,cAAc;AAAEM,IAAAA;AAAmB,GAAE,GAAGK,cAAc;EAC5E,MAAM;IAAEpB,EAAE;IAAEE,GAAG;AAAEvT,IAAAA;AAAI,GAAE,GAAGtD,YAAY;EACtC,MAAMkH,MAAM,GAAGyR,UAAU;EAEzB,IAAIpS,SAAS,GAAG,KAAK;AACrB,EAAA,IAAIwP,MAAkB;EACtB,IAAI6C,WAAW,GAAG3B,YAAY,CAACH,cAAc,EAAEuB,aAAa,CAACQ,aAAa,CAAC;AAC3E,EAAA,IAAIrd,OAAO,GAAGyb,YAAY,CAAC2B,WAAW,CAAC;EACvC,IAAIE,UAAU,GAAsB,EAAE;AACtC,EAAA,IAAIC,UAA4B;AAEhC,EAAA,IAAIjT,SAAsB;AAC1B,EAAA,IAAIC,MAAqB;EAEzB,SAASiT,aAAaA,GAAA;IACpB,MAAM;AAAElT,MAAAA,SAAS,EAAEmT,aAAa;AAAElT,MAAAA,MAAM,EAAEmT;AAAU,KAAE,GAAG1d,OAAO;AAEhE,IAAA,MAAM2d,eAAe,GAAG/hB,QAAQ,CAAC6hB,aAAa,CAAC,GAC3CvK,IAAI,CAAC0K,aAAa,CAACH,aAAa,CAAC,GACjCA,aAAa;IACjBnT,SAAS,GAAiBqT,eAAe,IAAIzK,IAAI,CAAC2K,QAAQ,CAAC,CAAC,CAAE;AAE9D,IAAA,MAAMC,YAAY,GAAGliB,QAAQ,CAAC8hB,UAAU,CAAC,GACrCpT,SAAS,CAACyT,gBAAgB,CAACL,UAAU,CAAC,GACtCA,UAAU;AACdnT,IAAAA,MAAM,GAAkB,EAAE,CAAC+E,KAAK,CAACpT,IAAI,CAAC4hB,YAAY,IAAIxT,SAAS,CAACuT,QAAQ,CAAC;AAC3E;EAEA,SAASG,YAAYA,CAAChe,OAAoB,EAAA;AACxC,IAAA,MAAMua,MAAM,GAAGX,MAAM,CACnB1G,IAAI,EACJ5I,SAAS,EACTC,MAAM,EACN3J,aAAa,EACb7B,WAAW,EACXiB,OAAO,EACPwE,YAAY,CACb;AAED,IAAA,IAAIxE,OAAO,CAACwD,IAAI,IAAI,CAAC+W,MAAM,CAACL,WAAW,CAACzD,OAAO,EAAE,EAAE;MACjD,MAAMwH,kBAAkB,GAAGliB,MAAM,CAAC4gB,MAAM,CAAC,EAAE,EAAE3c,OAAO,EAAE;AAAEwD,QAAAA,IAAI,EAAE;AAAK,OAAE,CAAC;MACtE,OAAOwa,YAAY,CAACC,kBAAkB,CAAC;AACzC;AACA,IAAA,OAAO1D,MAAM;AACf;AAEA,EAAA,SAAS2D,QAAQA,CACfC,WAA8B,EAC9BC,WAA+B,EAAA;AAE/B,IAAA,IAAIrT,SAAS,EAAE;AAEfqS,IAAAA,WAAW,GAAG3B,YAAY,CAAC2B,WAAW,EAAEe,WAAW,CAAC;AACpDne,IAAAA,OAAO,GAAG4b,cAAc,CAACwB,WAAW,CAAC;IACrCE,UAAU,GAAGc,WAAW,IAAId,UAAU;AAEtCE,IAAAA,aAAa,EAAE;AAEfjD,IAAAA,MAAM,GAAGyD,YAAY,CAAChe,OAAO,CAAC;IAE9Bkc,mBAAmB,CAAC,CAClBkB,WAAW,EACX,GAAGE,UAAU,CAAClgB,GAAG,CAAC,CAAC;AAAE4C,MAAAA;KAAS,KAAKA,OAAO,CAAC,CAC5C,CAAC,CAACtB,OAAO,CAAE2f,KAAK,IAAKnB,aAAa,CAACtd,GAAG,CAACye,KAAK,EAAE,QAAQ,EAAElB,UAAU,CAAC,CAAC;AAErE,IAAA,IAAI,CAACnd,OAAO,CAAC6N,MAAM,EAAE;AAErB0M,IAAAA,MAAM,CAAChG,SAAS,CAACM,EAAE,CAAC0F,MAAM,CAACpW,QAAQ,CAACP,GAAG,EAAE,CAAC;AAC1C2W,IAAAA,MAAM,CAACnW,SAAS,CAAChD,IAAI,EAAE;AACvBmZ,IAAAA,MAAM,CAACG,YAAY,CAACtZ,IAAI,EAAE;AAC1BmZ,IAAAA,MAAM,CAACI,UAAU,CAACvZ,IAAI,CAAC3B,IAAI,CAAC;AAC5B8a,IAAAA,MAAM,CAAC/V,YAAY,CAACpD,IAAI,CAAC3B,IAAI,CAAC;AAC9B8a,IAAAA,MAAM,CAACK,aAAa,CAACxZ,IAAI,CAAC3B,IAAI,CAAC;AAC/B8a,IAAAA,MAAM,CAACO,aAAa,CAAC1Z,IAAI,CAAC3B,IAAI,CAAC;AAE/B,IAAA,IAAI8a,MAAM,CAACva,OAAO,CAACwD,IAAI,EAAE+W,MAAM,CAACL,WAAW,CAAC1W,IAAI,EAAE;AAClD,IAAA,IAAI8G,SAAS,CAACgU,YAAY,IAAI/T,MAAM,CAAC9M,MAAM,EAAE8c,MAAM,CAACP,WAAW,CAAC5Y,IAAI,CAAC3B,IAAI,CAAC;IAE1E8d,UAAU,GAAGN,cAAc,CAAC7b,IAAI,CAAC3B,IAAI,EAAE6d,UAAU,CAAC;AACpD;AAEA,EAAA,SAASH,UAAUA,CACjBgB,WAA8B,EAC9BC,WAA+B,EAAA;AAE/B,IAAA,MAAMtE,UAAU,GAAGyE,kBAAkB,EAAE;AACvCC,IAAAA,UAAU,EAAE;IACZN,QAAQ,CAACzC,YAAY,CAAC;AAAE3B,MAAAA;AAAU,KAAE,EAAEqE,WAAW,CAAC,EAAEC,WAAW,CAAC;AAChE5Z,IAAAA,YAAY,CAACsD,IAAI,CAAC,QAAQ,CAAC;AAC7B;EAEA,SAAS0W,UAAUA,GAAA;AACjBjE,IAAAA,MAAM,CAACP,WAAW,CAACzY,OAAO,EAAE;AAC5BgZ,IAAAA,MAAM,CAACpH,UAAU,CAAC3S,KAAK,EAAE;AACzB+Z,IAAAA,MAAM,CAAChG,SAAS,CAAC/T,KAAK,EAAE;AACxB+Z,IAAAA,MAAM,CAACL,WAAW,CAAC1Z,KAAK,EAAE;AAC1B+Z,IAAAA,MAAM,CAACK,aAAa,CAACrZ,OAAO,EAAE;AAC9BgZ,IAAAA,MAAM,CAACO,aAAa,CAACvZ,OAAO,EAAE;AAC9BgZ,IAAAA,MAAM,CAACG,YAAY,CAACnZ,OAAO,EAAE;AAC7BgZ,IAAAA,MAAM,CAACnW,SAAS,CAAC7C,OAAO,EAAE;IAC1B0b,cAAc,CAAC1b,OAAO,EAAE;IACxB2b,aAAa,CAAC1c,KAAK,EAAE;AACvB;EAEA,SAASe,OAAOA,GAAA;AACd,IAAA,IAAIwJ,SAAS,EAAE;AACfA,IAAAA,SAAS,GAAG,IAAI;IAChBmS,aAAa,CAAC1c,KAAK,EAAE;AACrBge,IAAAA,UAAU,EAAE;AACZha,IAAAA,YAAY,CAACsD,IAAI,CAAC,SAAS,CAAC;IAC5BtD,YAAY,CAAChE,KAAK,EAAE;AACtB;AAEA,EAAA,SAAS6D,QAAQA,CAAC1G,KAAa,EAAE8gB,IAAc,EAAE3b,SAAkB,EAAA;AACjE,IAAA,IAAI,CAAC9C,OAAO,CAAC6N,MAAM,IAAI9C,SAAS,EAAE;AAClCwP,IAAAA,MAAM,CAACjW,UAAU,CACd0I,eAAe,EAAE,CACjBpF,WAAW,CAAC6W,IAAI,KAAK,IAAI,GAAG,CAAC,GAAGze,OAAO,CAAC6M,QAAQ,CAAC;IACpD0N,MAAM,CAAClW,QAAQ,CAAC1G,KAAK,CAACA,KAAK,EAAEmF,SAAS,IAAI,CAAC,CAAC;AAC9C;EAEA,SAAS4b,UAAUA,CAACD,IAAc,EAAA;AAChC,IAAA,MAAMvX,IAAI,GAAGqT,MAAM,CAAC5c,KAAK,CAACiC,GAAG,CAAC,CAAC,CAAC,CAACgE,GAAG,EAAE;AACtCS,IAAAA,QAAQ,CAAC6C,IAAI,EAAEuX,IAAI,EAAE,CAAC,CAAC,CAAC;AAC1B;EAEA,SAASE,UAAUA,CAACF,IAAc,EAAA;AAChC,IAAA,MAAMG,IAAI,GAAGrE,MAAM,CAAC5c,KAAK,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACgE,GAAG,EAAE;AACvCS,IAAAA,QAAQ,CAACua,IAAI,EAAEH,IAAI,EAAE,CAAC,CAAC;AACzB;EAEA,SAASI,aAAaA,GAAA;AACpB,IAAA,MAAM3X,IAAI,GAAGqT,MAAM,CAAC5c,KAAK,CAACiC,GAAG,CAAC,CAAC,CAAC,CAACgE,GAAG,EAAE;AACtC,IAAA,OAAOsD,IAAI,KAAKqX,kBAAkB,EAAE;AACtC;EAEA,SAASO,aAAaA,GAAA;AACpB,IAAA,MAAMF,IAAI,GAAGrE,MAAM,CAAC5c,KAAK,CAACiC,GAAG,CAAC,CAAC,CAAC,CAAC,CAACgE,GAAG,EAAE;AACvC,IAAA,OAAOgb,IAAI,KAAKL,kBAAkB,EAAE;AACtC;EAEA,SAAS1D,cAAcA,GAAA;IACrB,OAAON,MAAM,CAACM,cAAc;AAC9B;EAEA,SAASJ,cAAcA,GAAA;AACrB,IAAA,OAAOF,MAAM,CAACE,cAAc,CAAC7W,GAAG,CAAC2W,MAAM,CAACpW,QAAQ,CAACP,GAAG,EAAE,CAAC;AACzD;EAEA,SAAS2a,kBAAkBA,GAAA;AACzB,IAAA,OAAOhE,MAAM,CAAC5c,KAAK,CAACiG,GAAG,EAAE;AAC3B;EAEA,SAASmb,kBAAkBA,GAAA;AACzB,IAAA,OAAOxE,MAAM,CAAC1H,aAAa,CAACjP,GAAG,EAAE;AACnC;EAEA,SAAS8W,YAAYA,GAAA;AACnB,IAAA,OAAOH,MAAM,CAACG,YAAY,CAAC9W,GAAG,EAAE;AAClC;EAEA,SAASob,eAAeA,GAAA;AACtB,IAAA,OAAOzE,MAAM,CAACG,YAAY,CAAC9W,GAAG,CAAC,KAAK,CAAC;AACvC;EAEA,SAAS6Y,OAAOA,GAAA;AACd,IAAA,OAAOc,UAAU;AACnB;EAEA,SAAS0B,cAAcA,GAAA;AACrB,IAAA,OAAO1E,MAAM;AACf;EAEA,SAASvW,QAAQA,GAAA;AACf,IAAA,OAAOkP,IAAI;AACb;EAEA,SAASgM,aAAaA,GAAA;AACpB,IAAA,OAAO5U,SAAS;AAClB;EAEA,SAAS6U,UAAUA,GAAA;AACjB,IAAA,OAAO5U,MAAM;AACf;AAEA,EAAA,MAAM9K,IAAI,GAAsB;IAC9Bof,aAAa;IACbC,aAAa;IACbI,aAAa;IACbD,cAAc;IACd1d,OAAO;IACP8Z,GAAG;IACHF,EAAE;IACFrT,IAAI;IACJ2U,OAAO;IACPsC,kBAAkB;IAClBrT,MAAM;IACN1H,QAAQ;IACR0a,UAAU;IACVC,UAAU;IACVlE,cAAc;IACdI,cAAc;IACdxW,QAAQ;IACRka,kBAAkB;IAClBY,UAAU;IACVzE,YAAY;AACZsE,IAAAA;GACD;AAEDd,EAAAA,QAAQ,CAACpB,WAAW,EAAEC,WAAW,CAAC;EAClCqC,UAAU,CAAC,MAAM5a,YAAY,CAACsD,IAAI,CAAC,MAAM,CAAC,EAAE,CAAC,CAAC;AAC9C,EAAA,OAAOrI,IAAI;AACb;AAMAod,aAAa,CAACQ,aAAa,GAAGhX,SAAS;;;;"}