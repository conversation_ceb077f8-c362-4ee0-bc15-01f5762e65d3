package com.lumilove.service;

import com.fasterxml.jackson.databind.JsonNode;
import com.fasterxml.jackson.databind.ObjectMapper;
import com.lumilove.entity.Character;
import com.lumilove.entity.ChatHistory;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.beans.factory.annotation.Value;
import org.springframework.http.HttpEntity;
import org.springframework.http.HttpHeaders;
import org.springframework.http.HttpMethod;
import org.springframework.http.ResponseEntity;
import org.springframework.stereotype.Service;
import org.springframework.web.client.RestTemplate;

import java.util.HashMap;
import java.util.List;
import java.util.Map;

@Slf4j
@Service
@RequiredArgsConstructor
public class AIService {
    
    @Value("${ai.gemini.api-key}")
    private String geminiApiKey;
    
    @Value("${ai.gemini.base-url}")
    private String geminiBaseUrl;
    
    @Value("${ai.voice.minimax.api-key}")
    private String minimaxApiKey;
    
    @Value("${ai.voice.minimax.base-url}")
    private String minimaxBaseUrl;
    
    @Value("${ai.image-generation.api-key}")
    private String imageGenApiKey;
    
    @Value("${ai.image-generation.base-url}")
    private String imageGenBaseUrl;
    
    private final RestTemplate restTemplate = new RestTemplate();
    private final ObjectMapper objectMapper = new ObjectMapper();
    
    public String generateResponse(Character character, String userMessage, List<ChatHistory> chatHistory) {
        try {
            // Build conversation context
            StringBuilder contextBuilder = new StringBuilder();
            
            // Add character prompt
            if (character.getPromptConfig() != null) {
                contextBuilder.append("Character: ").append(character.getPromptConfig()).append("\n");
            } else {
                contextBuilder.append("You are ").append(character.getName())
                           .append(", a ").append(character.getOccupation())
                           .append(". ").append(character.getDescription()).append("\n");
            }
            
            // Add recent chat history
            for (ChatHistory history : chatHistory) {
                contextBuilder.append("User: ").append(history.getMessage()).append("\n");
                contextBuilder.append("Assistant: ").append(history.getResponse()).append("\n");
            }
            
            // Add current message
            contextBuilder.append("User: ").append(userMessage).append("\n");
            contextBuilder.append("Assistant: ");
            
            // Call Gemini API
            String url = geminiBaseUrl + "/models/gemini-pro:generateContent?key=" + geminiApiKey;
            
            Map<String, Object> requestBody = new HashMap<>();
            Map<String, Object> contents = new HashMap<>();
            Map<String, Object> parts = new HashMap<>();
            parts.put("text", contextBuilder.toString());
            contents.put("parts", List.of(parts));
            requestBody.put("contents", List.of(contents));
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            // Parse response
            JsonNode responseJson = objectMapper.readTree(response.getBody());
            JsonNode candidates = responseJson.get("candidates");
            if (candidates != null && candidates.isArray() && candidates.size() > 0) {
                JsonNode content = candidates.get(0).get("content");
                if (content != null) {
                    JsonNode parts = content.get("parts");
                    if (parts != null && parts.isArray() && parts.size() > 0) {
                        return parts.get(0).get("text").asText();
                    }
                }
            }
            
            return "I'm sorry, I couldn't generate a response right now. Please try again.";
            
        } catch (Exception e) {
            log.error("Error generating AI response", e);
            return "I'm having trouble responding right now. Please try again later.";
        }
    }
    
    public String generateVoiceResponse(String text, Character character) {
        try {
            // Call MiniMAX voice generation API
            String url = minimaxBaseUrl + "/v1/text_to_speech";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("text", text);
            requestBody.put("voice_id", getVoiceIdForCharacter(character));
            requestBody.put("speed", 1.0);
            requestBody.put("volume", 0.8);
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Bearer " + minimaxApiKey);
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            // Parse response to get audio URL
            JsonNode responseJson = objectMapper.readTree(response.getBody());
            return responseJson.get("audio_url").asText();
            
        } catch (Exception e) {
            log.error("Error generating voice response", e);
            return null;
        }
    }
    
    public String generateImageResponse(String prompt, Character character) {
        try {
            // Call image generation API (using Replicate as example)
            String url = imageGenBaseUrl + "/v1/predictions";
            
            Map<String, Object> requestBody = new HashMap<>();
            requestBody.put("version", "stability-ai/stable-diffusion:27b93a2413e7f36cd83da926f3656280b2931564ff050bf9575f1fdf9bcd7478");
            
            Map<String, Object> input = new HashMap<>();
            input.put("prompt", buildImagePrompt(prompt, character));
            input.put("width", 512);
            input.put("height", 512);
            input.put("num_inference_steps", 20);
            requestBody.put("input", input);
            
            HttpHeaders headers = new HttpHeaders();
            headers.set("Authorization", "Token " + imageGenApiKey);
            headers.set("Content-Type", "application/json");
            
            HttpEntity<Map<String, Object>> entity = new HttpEntity<>(requestBody, headers);
            ResponseEntity<String> response = restTemplate.exchange(url, HttpMethod.POST, entity, String.class);
            
            // Parse response to get image URL
            JsonNode responseJson = objectMapper.readTree(response.getBody());
            return responseJson.get("urls").get("get").asText();
            
        } catch (Exception e) {
            log.error("Error generating image response", e);
            return null;
        }
    }
    
    private String getVoiceIdForCharacter(Character character) {
        // Map character to appropriate voice ID
        if ("female".equals(character.getGender())) {
            return "female_voice_1";
        } else {
            return "male_voice_1";
        }
    }
    
    private String buildImagePrompt(String userPrompt, Character character) {
        StringBuilder promptBuilder = new StringBuilder();
        promptBuilder.append("Portrait of ").append(character.getName())
                    .append(", ").append(character.getDescription())
                    .append(", ").append(userPrompt)
                    .append(", high quality, detailed, professional photography");
        
        return promptBuilder.toString();
    }
}
