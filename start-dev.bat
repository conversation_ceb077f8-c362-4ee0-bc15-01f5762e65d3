@echo off
echo 🚀 Starting Lumilove in Development Mode...

REM Check if required tools are installed
java -version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Java is not installed. Please install Java 17 or higher.
    pause
    exit /b 1
)

node --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Node.js is not installed. Please install Node.js 18 or higher.
    pause
    exit /b 1
)

pnpm --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ pnpm is not installed. Installing pnpm...
    npm install -g pnpm
)

REM Start database and Redis with Docker
echo 🗄️  Starting database and Redis...
docker-compose up -d mysql redis

REM Wait for database to be ready
echo ⏳ Waiting for database to be ready...
timeout /t 15 /nobreak >nul

REM Install frontend dependencies
echo 📦 Installing frontend dependencies...
cd "lumilove (1)"
pnpm install

REM Start frontend in development mode
echo 🎨 Starting frontend development server...
start "Frontend" cmd /k "pnpm dev"

REM Go back to root directory
cd ..

REM Start backend in development mode
echo 🔧 Starting backend development server...
cd backend
start "Backend" cmd /k "mvnw.cmd spring-boot:run"

REM Go back to root directory
cd ..

echo.
echo ✅ Development environment is starting up!
echo.
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8080/api
echo 🗄️  Database: localhost:3306
echo 🔴 Redis: localhost:6379
echo.
echo 📋 Default admin credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo 🛑 To stop development servers:
echo    Close the Frontend and Backend command windows
echo    Or run: docker-compose stop mysql redis
echo.

echo 📋 Development servers are starting in separate windows...
pause
