@echo off
echo 🚀 Starting Lumilove Test Environment...

REM Start only database and Redis
echo 🗄️  Starting database and Redis...
docker-compose -f docker-compose.simple.yml up -d

echo ⏳ Waiting for database to be ready...
timeout /t 20 /nobreak >nul

echo.
echo ✅ Database and Redis are ready!
echo.
echo 🗄️  Database: localhost:3306
echo    Username: lumilove
echo    Password: password
echo    Database: lumilove
echo.
echo 🔴 Redis: localhost:6379
echo.
echo 📋 Next steps:
echo 1. Start backend: cd backend && mvnw.cmd spring-boot:run
echo 2. Start frontend: cd "lumilove (1)" && pnpm dev
echo 3. Visit: http://localhost:3000
echo.
echo 🛑 To stop: docker-compose -f docker-compose.simple.yml down
echo.

pause
