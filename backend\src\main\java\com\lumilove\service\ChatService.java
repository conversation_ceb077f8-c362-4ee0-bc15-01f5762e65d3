package com.lumilove.service;

import com.lumilove.dto.ChatRequest;
import com.lumilove.dto.ChatResponse;
import com.lumilove.entity.ChatHistory;
import com.lumilove.entity.Character;
import com.lumilove.entity.User;
import com.lumilove.repository.ChatHistoryRepository;
import com.lumilove.repository.CharacterRepository;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.data.domain.PageRequest;
import org.springframework.data.domain.Pageable;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.util.List;
import java.util.stream.Collectors;

@Slf4j
@Service
@RequiredArgsConstructor
public class ChatService {
    
    private final ChatHistoryRepository chatHistoryRepository;
    private final CharacterRepository characterRepository;
    private final UserService userService;
    private final AIService aiService;
    
    public List<ChatResponse> getChatHistory(Long userId, Long characterId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        List<ChatHistory> chatHistories = chatHistoryRepository
                .findByUserIdAndCharacterIdOrderByCreatedAtDesc(userId, characterId, pageable);
        
        return chatHistories.stream()
                .map(ChatResponse::fromChatHistory)
                .collect(Collectors.toList());
    }
    
    public List<Long> getRecentChatCharacterIds(Long userId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return chatHistoryRepository.findRecentChatCharacterIds(userId, pageable);
    }
    
    @Transactional
    public ChatHistory saveChatMessage(Long userId, Long characterId, String message, String messageType) {
        ChatHistory chatHistory = new ChatHistory();
        chatHistory.setUserId(userId);
        chatHistory.setCharacterId(characterId);
        chatHistory.setMessage(message);
        chatHistory.setResponse(""); // Will be filled by AI response
        chatHistory.setMessageType(messageType);
        chatHistory.setIsUserMessage(true);
        
        return chatHistoryRepository.save(chatHistory);
    }
    
    @Transactional
    public ChatHistory saveChatResponse(Long userId, Long characterId, String userMessage, 
                                      String aiResponse, String messageType, String responseType,
                                      String audioUrl, String imageUrl, Integer audioDuration) {
        ChatHistory chatHistory = new ChatHistory();
        chatHistory.setUserId(userId);
        chatHistory.setCharacterId(characterId);
        chatHistory.setMessage(userMessage);
        chatHistory.setResponse(aiResponse);
        chatHistory.setMessageType(messageType);
        chatHistory.setResponseType(responseType);
        chatHistory.setAudioUrl(audioUrl);
        chatHistory.setImageUrl(imageUrl);
        chatHistory.setAudioDuration(audioDuration);
        chatHistory.setIsUserMessage(false);
        
        return chatHistoryRepository.save(chatHistory);
    }
    
    @Transactional
    public String processChat(ChatRequest chatRequest, Long userId) {
        // Get character
        Character character = characterRepository.findById(chatRequest.getCharacterId())
                .orElseThrow(() -> new RuntimeException("Character not found"));
        
        // Check quotas for special response types
        if ("picture".equals(chatRequest.getResponseType())) {
            if (!userService.canUsePictureFeature(userId)) {
                throw new RuntimeException("Picture quota exceeded. Please upgrade to premium.");
            }
            userService.consumePictureQuota(userId);
        } else if ("voice".equals(chatRequest.getResponseType())) {
            if (!userService.canUseVoiceFeature(userId)) {
                throw new RuntimeException("Voice quota exceeded. Please upgrade to premium.");
            }
            userService.consumeVoiceQuota(userId);
        }
        
        // Get recent chat history for context
        List<ChatHistory> recentHistory = chatHistoryRepository
                .findRecentChatHistory(userId, chatRequest.getCharacterId(), PageRequest.of(0, 5));
        
        // Generate AI response
        String aiResponse = aiService.generateResponse(character, chatRequest.getMessage(), recentHistory);
        
        // Handle different response types
        String audioUrl = null;
        String imageUrl = null;
        Integer audioDuration = null;
        
        if ("voice".equals(chatRequest.getResponseType())) {
            // Generate voice response
            audioUrl = aiService.generateVoiceResponse(aiResponse, character);
            audioDuration = calculateAudioDuration(aiResponse); // Estimate duration
        } else if ("picture".equals(chatRequest.getResponseType())) {
            // Generate image response
            imageUrl = aiService.generateImageResponse(aiResponse, character);
        }
        
        // Save chat history
        saveChatResponse(userId, chatRequest.getCharacterId(), chatRequest.getMessage(), 
                        aiResponse, chatRequest.getMessageType(), chatRequest.getResponseType(),
                        audioUrl, imageUrl, audioDuration);
        
        // Increment character usage
        characterRepository.findById(chatRequest.getCharacterId())
                .ifPresent(c -> {
                    c.incrementUsage();
                    characterRepository.save(c);
                });
        
        return aiResponse;
    }
    
    private Integer calculateAudioDuration(String text) {
        // Rough estimation: 150 words per minute, average 5 characters per word
        int estimatedWords = text.length() / 5;
        return Math.max(1, (estimatedWords * 60) / 150); // Duration in seconds
    }
    
    public List<ChatHistory> buildChatHistory(Long userId, Long characterId, int limit) {
        Pageable pageable = PageRequest.of(0, limit);
        return chatHistoryRepository.findRecentChatHistory(userId, characterId, pageable);
    }
}
