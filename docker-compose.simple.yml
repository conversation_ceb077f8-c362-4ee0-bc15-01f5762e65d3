version: '3.8'

services:
  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: lumilove-mysql
    environment:
      MYSQL_ROOT_PASSWORD: password
      MYSQL_DATABASE: lumilove
      MYSQL_USER: lumilove
      MYSQL_PASSWORD: password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./backend/src/main/resources/schema.sql:/docker-entrypoint-initdb.d/1-schema.sql
      - ./backend/src/main/resources/data.sql:/docker-entrypoint-initdb.d/2-data.sql
    command: --default-authentication-plugin=mysql_native_password

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: lumilove-redis
    ports:
      - "6379:6379"
    volumes:
      - redis_data:/data

volumes:
  mysql_data:
  redis_data:
