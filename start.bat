@echo off
echo 🚀 Starting Lumilove AI Companion Platform...

REM Check if Docker is installed
docker --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker is not installed. Please install Docker Desktop first.
    pause
    exit /b 1
)

REM Check if Docker Compose is installed
docker-compose --version >nul 2>&1
if %errorlevel% neq 0 (
    echo ❌ Docker Compose is not installed. Please install Docker Compose first.
    pause
    exit /b 1
)

REM Create necessary directories
if not exist logs mkdir logs

REM Stop any existing containers
echo 🛑 Stopping existing containers...
docker-compose down

REM Build and start services
echo 🔨 Building and starting services...
docker-compose up --build -d

REM Wait for services to be ready
echo ⏳ Waiting for services to be ready...
timeout /t 30 /nobreak >nul

REM Check if services are running
echo 🔍 Checking service status...
docker-compose ps

echo.
echo ✅ Lumilove platform is starting up!
echo.
echo 📱 Frontend: http://localhost:3000
echo 🔧 Backend API: http://localhost:8080/api
echo 🗄️  Database: localhost:3306
echo 🔴 Redis: localhost:6379
echo.
echo 📋 Default admin credentials:
echo    Username: admin
echo    Password: admin123
echo.
echo 📝 To view logs:
echo    docker-compose logs -f [service-name]
echo.
echo 🛑 To stop all services:
echo    docker-compose down
echo.

echo 📋 Following application logs (Ctrl+C to exit)...
docker-compose logs -f
