import { Check, CreditCard, Wallet } from "lucide-react"
import Sidebar from "@/components/sidebar"

export default function PremiumPage() {
  const benefits = [
    "Create your own characters",
    "Video Generation",
    "Image Generation",
    "Ask for photos",
    "Voice / Video Calls",
    "1000 coins per month",
    "Priority access to new features",
  ]

  return (
    <div className="flex min-h-screen">
      <Sidebar />

      <main className="flex-1 p-6">
        <div className="max-w-4xl mx-auto">
          <div className="text-center mb-10">
            <h1 className="text-3xl font-bold mb-4">Pro Benefits</h1>
            <div className="space-y-2">
              {benefits.map((benefit, index) => (
                <div key={index} className="flex items-center justify-center">
                  <Check className="h-5 w-5 text-pink-500 mr-2" />
                  <span>{benefit}</span>
                </div>
              ))}
            </div>
            <div className="mt-6 inline-block bg-gradient-to-r from-pink-500 to-pink-600 text-white px-6 py-2 rounded-full font-bold text-lg">
              🔥 75% OFF First Year — Limited Time Only!
            </div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-10">
            {/* Monthly plan */}
            <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44]">
              <div className="text-center mb-6">
                <h2 className="text-xl font-bold mb-2">1 month</h2>
                <div className="flex items-center justify-center">
                  <div className="text-gray-400 line-through mr-2">$19.99</div>
                  <div className="text-2xl font-bold">$9.99</div>
                </div>
                <div className="text-sm text-gray-400">First Month, then $19.99</div>
              </div>
              <button className="w-full bg-pink-500 hover:bg-pink-600 text-white py-3 rounded-lg font-medium mb-4">
                Subscribe Now
              </button>
              <div className="text-center text-sm text-gray-400">Cancel anytime</div>
            </div>

            {/* Annual plan */}
            <div className="bg-[#1a0a24] rounded-xl p-6 border border-[#3a1a44] relative">
              <div className="absolute top-0 right-0 bg-pink-500 text-white px-3 py-1 rounded-bl-lg rounded-tr-lg text-sm font-medium">
                Best Value
              </div>
              <div className="text-center mb-6">
                <h2 className="text-xl font-bold mb-2">12 months</h2>
                <div className="flex items-center justify-center">
                  <div className="text-gray-400 line-through mr-2">$119.99</div>
                  <div className="text-2xl font-bold">$59.99</div>
                </div>
                <div className="text-sm text-gray-400">First Year, then $119.99</div>
                <div className="mt-2 text-pink-500 font-medium">Save 75%</div>
              </div>
              <button className="w-full bg-pink-500 hover:bg-pink-600 text-white py-3 rounded-lg font-medium mb-4">
                Subscribe Now
              </button>
              <div className="text-center text-sm text-gray-400">Cancel anytime</div>
            </div>
          </div>

          <div className="flex flex-col items-center space-y-4">
            <button className="w-full max-w-md flex items-center justify-center bg-[#1a0a24] hover:bg-[#2a1a34] py-3 rounded-lg">
              <CreditCard className="h-5 w-5 mr-2" />
              Pay with Credit Card
            </button>
            <button className="w-full max-w-md flex items-center justify-center bg-[#1a0a24] hover:bg-[#2a1a34] py-3 rounded-lg">
              <Wallet className="h-5 w-5 mr-2" />
              Pay with Wallet
            </button>
            <button className="text-gray-400 hover:text-pink-400">▾ More Payment Options</button>
          </div>

          <div className="mt-10 text-center text-sm text-gray-400">
            <p>
              By subscribing, you agree to our Terms of Service and Privacy Policy. Your subscription will automatically
              renew unless canceled at least 24 hours before the end of the current period.
            </p>
          </div>
        </div>
      </main>
    </div>
  )
}
