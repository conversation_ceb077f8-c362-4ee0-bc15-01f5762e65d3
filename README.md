# 🌟 Lumilove - AI Companion Platform

Lumilove is a comprehensive AI companion platform that allows users to chat with virtual characters, generate images, and access premium content through a subscription model.

## ✨ Features

### 🎭 Character System
- **Public Character Gallery**: Browse and interact with pre-made characters
- **Character Creation**: Create custom AI companions with unique personalities
- **Gender-based Filtering**: Separate male and female character categories
- **Trending Characters**: Discover popular characters based on usage

### 💬 Advanced Chat System
- **Real-time Messaging**: Instant responses from AI characters
- **Multiple Response Types**: 
  - Text responses (unlimited)
  - Picture generation (limited for free users)
  - Voice responses (limited for free users)
- **Chat History**: Persistent conversation history
- **Streaming Responses**: Real-time message streaming for better UX

### 🎨 Content Generation
- **AI Image Generation**: Create custom images with characters
- **Voice Synthesis**: Generate voice messages from characters
- **Scene Customization**: Choose backgrounds, poses, and clothing

### 🔒 Premium Features
- **Subscription System**: Monthly and annual plans
- **Quota Management**: Free users have limited access to premium features
- **Premium Content**: Access to exclusive characters and content

### 🔐 User Management
- **Authentication**: Secure login and registration
- **User Profiles**: Personal avatars and preferences
- **Balance System**: Virtual currency for purchases

## 🛠️ Technology Stack

### Frontend
- **Next.js 15** - React framework with App Router
- **TypeScript** - Type-safe development
- **Tailwind CSS** - Utility-first CSS framework
- **Radix UI** - Accessible component library
- **Lucide React** - Beautiful icons

### Backend
- **Spring Boot 3.2** - Java web framework
- **Spring Security** - Authentication and authorization
- **Spring Data JPA** - Database abstraction
- **MySQL 8.0** - Primary database
- **Redis** - Caching and session management
- **JWT** - Token-based authentication

### AI Services
- **Google Gemini** - Large Language Model for conversations
- **Replicate** - Image generation API
- **MiniMAX** - Voice synthesis API

### Infrastructure
- **Docker** - Containerization
- **Docker Compose** - Multi-container orchestration
- **Aliyun OSS** - File storage (optional)
- **Stripe** - Payment processing (optional)

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Git

### Option 1: Docker (Recommended)
```bash
# Clone the repository
git clone <repository-url>
cd lumilove

# Make start script executable
chmod +x start.sh

# Start the application
./start.sh
```

### Option 2: Development Mode
```bash
# Prerequisites for development mode
# - Java 17+
# - Node.js 18+
# - MySQL 8.0
# - Redis

# Make development script executable
chmod +x start-dev.sh

# Start in development mode
./start-dev.sh
```

## 🌐 Access Points

- **Frontend**: http://localhost:3000
- **Backend API**: http://localhost:8080/api
- **Database**: localhost:3306
- **Redis**: localhost:6379

## 👤 Default Credentials

- **Username**: admin
- **Password**: admin123

## 📁 Project Structure

```
lumilove/
├── backend/                 # Spring Boot backend
│   ├── src/main/java/      # Java source code
│   ├── src/main/resources/ # Configuration files
│   └── pom.xml             # Maven dependencies
├── lumilove (1)/           # Next.js frontend
│   ├── app/                # App Router pages
│   ├── components/         # React components
│   ├── lib/                # Utility libraries
│   └── contexts/           # React contexts
├── docker-compose.yml      # Docker services
├── start.sh               # Production start script
├── start-dev.sh           # Development start script
└── README.md              # This file
```

## 🔧 Configuration

### Environment Variables

#### Backend (.env)
```bash
# Database
DB_USERNAME=root
DB_PASSWORD=password

# AI Services
GEMINI_API_KEY=your-gemini-api-key
MINIMAX_API_KEY=your-minimax-api-key
IMAGE_GEN_API_KEY=your-image-gen-api-key

# Optional: Stripe for payments
STRIPE_SECRET_KEY=sk_test_your_secret_key
```

#### Frontend (.env.local)
```bash
NEXT_PUBLIC_API_URL=http://localhost:8080/api
```

## 🔌 API Endpoints

### Authentication
- `POST /api/auth/login` - User login
- `POST /api/auth/register` - User registration
- `GET /api/auth/me` - Get current user

### Characters
- `GET /api/characters/public` - Get public characters
- `GET /api/characters/public/gender/{gender}` - Get characters by gender
- `GET /api/characters/public/trending` - Get trending characters
- `POST /api/characters` - Create character (authenticated)

### Chat
- `GET /api/chat/history/{characterId}` - Get chat history
- `POST /api/chat/send` - Send message
- `POST /api/chat/stream` - Streaming chat (SSE)

## 🧪 Testing

### Backend Tests
```bash
cd backend
./mvnw test
```

### Frontend Tests
```bash
cd "lumilove (1)"
pnpm test
```

## 📦 Deployment

### Production Deployment
1. Update environment variables for production
2. Configure SSL certificates
3. Set up domain and DNS
4. Use production database
5. Configure CDN for static assets

### Docker Production
```bash
# Build production images
docker-compose -f docker-compose.prod.yml up --build -d
```

## 🤝 Contributing

1. Fork the repository
2. Create a feature branch
3. Make your changes
4. Add tests if applicable
5. Submit a pull request

## 📄 License

This project is licensed under the MIT License.

## 🆘 Support

For support and questions:
- Create an issue in the repository
- Check the documentation
- Review the logs: `docker-compose logs -f`

## 🔄 Updates

To update the application:
```bash
git pull origin main
docker-compose down
docker-compose up --build -d
```
