import { AnimationsType } from './Animations';
import { AxisType } from './Axis';
import { CounterType } from './Counter';
import { DragHandlerType } from './DragHandler';
import { EventHandlerType } from './EventHandler';
import { EventStoreType } from './EventStore';
import { LimitType } from './Limit';
import { NodeRectType } from './NodeRects';
import { OptionsType } from './Options';
import { PercentOfViewType } from './PercentOfView';
import { ResizeHandlerType } from './ResizeHandler';
import { ScrollBodyType } from './ScrollBody';
import { ScrollBoundsType } from './ScrollBounds';
import { ScrollLooperType } from './ScrollLooper';
import { ScrollProgressType } from './ScrollProgress';
import { SlideRegistryType } from './SlideRegistry';
import { ScrollTargetType } from './ScrollTarget';
import { ScrollToType } from './ScrollTo';
import { SlideFocusType } from './SlideFocus';
import { SlideLooperType } from './SlideLooper';
import { SlidesHandlerType } from './SlidesHandler';
import { SlidesInViewType } from './SlidesInView';
import { SlidesToScrollType } from './SlidesToScroll';
import { TranslateType } from './Translate';
import { WindowType } from './utils';
import { Vector1DType } from './Vector1d';
export type EngineType = {
    ownerDocument: Document;
    ownerWindow: WindowType;
    eventHandler: EventHandlerType;
    axis: AxisType;
    animation: AnimationsType;
    scrollBounds: ScrollBoundsType;
    scrollLooper: ScrollLooperType;
    scrollProgress: ScrollProgressType;
    index: CounterType;
    indexPrevious: CounterType;
    limit: LimitType;
    location: Vector1DType;
    offsetLocation: Vector1DType;
    previousLocation: Vector1DType;
    options: OptionsType;
    percentOfView: PercentOfViewType;
    scrollBody: ScrollBodyType;
    dragHandler: DragHandlerType;
    eventStore: EventStoreType;
    slideLooper: SlideLooperType;
    slidesInView: SlidesInViewType;
    slidesToScroll: SlidesToScrollType;
    target: Vector1DType;
    translate: TranslateType;
    resizeHandler: ResizeHandlerType;
    slidesHandler: SlidesHandlerType;
    scrollTo: ScrollToType;
    scrollTarget: ScrollTargetType;
    scrollSnapList: number[];
    scrollSnaps: number[];
    slideIndexes: number[];
    slideFocus: SlideFocusType;
    slideRegistry: SlideRegistryType['slideRegistry'];
    containerRect: NodeRectType;
    slideRects: NodeRectType[];
};
export declare function Engine(root: HTMLElement, container: HTMLElement, slides: HTMLElement[], ownerDocument: Document, ownerWindow: WindowType, options: OptionsType, eventHandler: EventHandlerType): EngineType;
