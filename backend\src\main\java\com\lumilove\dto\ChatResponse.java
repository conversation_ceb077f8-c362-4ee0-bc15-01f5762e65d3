package com.lumilove.dto;

import lombok.Data;

import java.time.LocalDateTime;

@Data
public class ChatResponse {
    private Long id;
    private String message;
    private String response;
    private String messageType;
    private String responseType;
    private String audioUrl;
    private String imageUrl;
    private Integer audioDuration;
    private Boolean isUserMessage;
    private LocalDateTime timestamp;
    
    public static ChatResponse fromChatHistory(com.lumilove.entity.ChatHistory chatHistory) {
        ChatResponse response = new ChatResponse();
        response.setId(chatHistory.getId());
        response.setMessage(chatHistory.getMessage());
        response.setResponse(chatHistory.getResponse());
        response.setMessageType(chatHistory.getMessageType());
        response.setResponseType(chatHistory.getResponseType());
        response.setAudioUrl(chatHistory.getAudioUrl());
        response.setImageUrl(chatHistory.getImageUrl());
        response.setAudioDuration(chatHistory.getAudioDuration());
        response.setIsUserMessage(chatHistory.getIsUserMessage());
        response.setTimestamp(chatHistory.getCreatedAt());
        return response;
    }
}
