package com.lumilove.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;

import java.math.BigDecimal;

@Data
@Entity
@Table(name = "orders")
@EqualsAndHashCode(callSuper = true)
public class Order extends BaseEntity {
    
    @Id
    private String id; // Order ID
    
    @Column(name = "user_id", nullable = false)
    private Long userId;
    
    @Column(name = "character_id")
    private Long characterId;
    
    @Column(nullable = false, precision = 10, scale = 2)
    private BigDecimal amount;
    
    @Enumerated(EnumType.STRING)
    @Column(nullable = false)
    private OrderStatus status = OrderStatus.PENDING;
    
    @Column(name = "payment_gateway", length = 20)
    private String paymentGateway;
    
    @Column(length = 3)
    private String currency = "USD";
    
    @Enumerated(EnumType.STRING)
    @Column(name = "refund_status")
    private RefundStatus refundStatus = RefundStatus.NONE;
    
    @Column(name = "stripe_payment_intent_id")
    private String stripePaymentIntentId;
    
    @Column(name = "order_type")
    private String orderType; // subscription, character_unlock, coins
    
    @Column(name = "subscription_type")
    private String subscriptionType; // monthly, annual
    
    @Column(name = "coins_amount")
    private Integer coinsAmount;
    
    // Foreign key relationships
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "user_id", insertable = false, updatable = false)
    private User user;
    
    @ManyToOne(fetch = FetchType.LAZY)
    @JoinColumn(name = "character_id", insertable = false, updatable = false)
    private Character character;
    
    public enum OrderStatus {
        PENDING, PAID, EXPIRED, CANCELLED
    }
    
    public enum RefundStatus {
        NONE, REQUESTED, COMPLETED
    }
}
