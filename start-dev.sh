#!/bin/bash

echo "🚀 Starting Lumilove in Development Mode..."

# Check if required tools are installed
if ! command -v java &> /dev/null; then
    echo "❌ Java is not installed. Please install Java 17 or higher."
    exit 1
fi

if ! command -v node &> /dev/null; then
    echo "❌ Node.js is not installed. Please install Node.js 18 or higher."
    exit 1
fi

if ! command -v pnpm &> /dev/null; then
    echo "❌ pnpm is not installed. Installing pnpm..."
    npm install -g pnpm
fi

# Start database and Redis with Docker
echo "🗄️  Starting database and Redis..."
docker-compose up -d mysql redis

# Wait for database to be ready
echo "⏳ Waiting for database to be ready..."
sleep 15

# Install frontend dependencies
echo "📦 Installing frontend dependencies..."
cd "lumilove (1)"
pnpm install

# Start frontend in development mode
echo "🎨 Starting frontend development server..."
pnpm dev &
FRONTEND_PID=$!

# Go back to root directory
cd ..

# Start backend in development mode
echo "🔧 Starting backend development server..."
cd backend
./mvnw spring-boot:run &
BACKEND_PID=$!

# Go back to root directory
cd ..

echo ""
echo "✅ Development environment is starting up!"
echo ""
echo "📱 Frontend: http://localhost:3000"
echo "🔧 Backend API: http://localhost:8080/api"
echo "🗄️  Database: localhost:3306"
echo "🔴 Redis: localhost:6379"
echo ""
echo "📋 Default admin credentials:"
echo "   Username: admin"
echo "   Password: admin123"
echo ""
echo "🛑 To stop development servers:"
echo "   Press Ctrl+C or run: kill $FRONTEND_PID $BACKEND_PID"
echo ""

# Function to cleanup on exit
cleanup() {
    echo "🛑 Stopping development servers..."
    kill $FRONTEND_PID 2>/dev/null
    kill $BACKEND_PID 2>/dev/null
    docker-compose stop mysql redis
    exit 0
}

# Set trap to cleanup on script exit
trap cleanup SIGINT SIGTERM

# Wait for user to stop
echo "📋 Development servers are running. Press Ctrl+C to stop..."
wait
