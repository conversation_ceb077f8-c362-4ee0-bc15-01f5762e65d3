package com.lumilove.entity;

import jakarta.persistence.*;
import lombok.Data;
import lombok.EqualsAndHashCode;
import org.springframework.security.core.GrantedAuthority;
import org.springframework.security.core.userdetails.UserDetails;

import java.math.BigDecimal;
import java.time.LocalDateTime;
import java.util.Collection;
import java.util.Collections;

@Data
@Entity
@Table(name = "users")
@EqualsAndHashCode(callSuper = true)
public class User extends BaseEntity implements UserDetails {
    
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;
    
    @Column(unique = true, nullable = false, length = 50)
    private String username;
    
    @Column(unique = true, nullable = false, length = 100)
    private String email;
    
    @Column(name = "password_hash", nullable = false, length = 100)
    private String password;
    
    @Column(precision = 10, scale = 2)
    private BigDecimal balance = BigDecimal.ZERO;
    
    @Column(name = "is_premium")
    private Boolean isPremium = false;
    
    @Column(name = "premium_expires_at")
    private LocalDateTime premiumExpiresAt;
    
    @Column(name = "picture_quota")
    private Integer pictureQuota = 3;
    
    @Column(name = "voice_quota")
    private Integer voiceQuota = 3;
    
    @Column(name = "avatar_url")
    private String avatarUrl;
    
    @Column(name = "is_active")
    private Boolean isActive = true;
    
    @Column(name = "last_login_at")
    private LocalDateTime lastLoginAt;
    
    @Column(name = "stripe_customer_id")
    private String stripeCustomerId;
    
    // UserDetails implementation
    @Override
    public Collection<? extends GrantedAuthority> getAuthorities() {
        return Collections.emptyList();
    }
    
    @Override
    public String getPassword() {
        return password;
    }
    
    @Override
    public String getUsername() {
        return username;
    }
    
    @Override
    public boolean isAccountNonExpired() {
        return true;
    }
    
    @Override
    public boolean isAccountNonLocked() {
        return isActive;
    }
    
    @Override
    public boolean isCredentialsNonExpired() {
        return true;
    }
    
    @Override
    public boolean isEnabled() {
        return isActive;
    }
    
    // Helper methods
    public boolean hasValidPremium() {
        return isPremium && premiumExpiresAt != null && premiumExpiresAt.isAfter(LocalDateTime.now());
    }
    
    public void consumePictureQuota() {
        if (!hasValidPremium() && pictureQuota > 0) {
            pictureQuota--;
        }
    }
    
    public void consumeVoiceQuota() {
        if (!hasValidPremium() && voiceQuota > 0) {
            voiceQuota--;
        }
    }
    
    public void resetQuotas() {
        pictureQuota = 3;
        voiceQuota = 3;
    }
}
