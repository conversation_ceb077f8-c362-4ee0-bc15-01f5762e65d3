package com.lumilove.repository;

import com.lumilove.entity.Character;
import org.springframework.data.domain.Page;
import org.springframework.data.domain.Pageable;
import org.springframework.data.jpa.repository.JpaRepository;
import org.springframework.data.jpa.repository.Query;
import org.springframework.data.repository.query.Param;
import org.springframework.stereotype.Repository;

import java.util.List;

@Repository
public interface CharacterRepository extends JpaRepository<Character, Long> {
    
    List<Character> findByCreatorId(Long creatorId);
    
    List<Character> findByIsPublicTrue();
    
    Page<Character> findByIsPublicTrue(Pageable pageable);
    
    List<Character> findByCategoryId(Integer categoryId);
    
    @Query("SELECT c FROM Character c WHERE c.isPublic = true AND c.gender = :gender")
    List<Character> findByGender(@Param("gender") String gender);
    
    @Query("SELECT c FROM Character c WHERE c.isPublic = true ORDER BY c.usageCount DESC")
    List<Character> findTrendingCharacters(Pageable pageable);
    
    @Query("SELECT c FROM Character c WHERE c.isPublic = true AND " +
           "(LOWER(c.name) LIKE LOWER(CONCAT('%', :keyword, '%')) OR " +
           "LOWER(c.description) LIKE LOWER(CONCAT('%', :keyword, '%')))")
    List<Character> searchCharacters(@Param("keyword") String keyword);
    
    @Query("SELECT c FROM Character c WHERE c.isPublic = true AND c.tags LIKE %:tag%")
    List<Character> findByTag(@Param("tag") String tag);
}
