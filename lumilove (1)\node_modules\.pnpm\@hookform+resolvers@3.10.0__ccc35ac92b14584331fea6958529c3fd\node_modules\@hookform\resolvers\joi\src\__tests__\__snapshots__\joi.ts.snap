// Vitest Snapshot v1, https://vitest.dev/guide/snapshot.html

exports[`joiResolver > should return a single error from joiResolver when validation fails 1`] = `
{
  "errors": {
    "birthYear": {
      "message": ""birthYear" must be a number",
      "ref": undefined,
      "type": "number.base",
    },
    "email": {
      "message": ""email" is not allowed to be empty",
      "ref": {
        "name": "email",
      },
      "type": "string.empty",
    },
    "enabled": {
      "message": ""enabled" is required",
      "ref": undefined,
      "type": "any.required",
    },
    "like": [
      {
        "id": {
          "message": ""like[0].id" must be a number",
          "ref": undefined,
          "type": "number.base",
        },
        "name": {
          "message": ""like[0].name" length must be 4 characters long",
          "ref": undefined,
          "type": "string.length",
        },
      },
    ],
    "password": {
      "message": ""password" with value "___" fails to match the One uppercase character pattern",
      "ref": {
        "name": "password",
      },
      "type": "string.pattern.name",
    },
    "tags": {
      "message": ""tags" is required",
      "ref": undefined,
      "type": "any.required",
    },
    "username": {
      "message": ""username" is required",
      "ref": {
        "name": "username",
      },
      "type": "any.required",
    },
  },
  "values": {},
}
`;

exports[`joiResolver > should return a single error from joiResolver with \`mode: sync\` when validation fails 1`] = `
{
  "errors": {
    "birthYear": {
      "message": ""birthYear" must be a number",
      "ref": undefined,
      "type": "number.base",
    },
    "email": {
      "message": ""email" is not allowed to be empty",
      "ref": {
        "name": "email",
      },
      "type": "string.empty",
    },
    "enabled": {
      "message": ""enabled" is required",
      "ref": undefined,
      "type": "any.required",
    },
    "like": [
      {
        "id": {
          "message": ""like[0].id" must be a number",
          "ref": undefined,
          "type": "number.base",
        },
        "name": {
          "message": ""like[0].name" length must be 4 characters long",
          "ref": undefined,
          "type": "string.length",
        },
      },
    ],
    "password": {
      "message": ""password" with value "___" fails to match the One uppercase character pattern",
      "ref": {
        "name": "password",
      },
      "type": "string.pattern.name",
    },
    "tags": {
      "message": ""tags" is required",
      "ref": undefined,
      "type": "any.required",
    },
    "username": {
      "message": ""username" is required",
      "ref": {
        "name": "username",
      },
      "type": "any.required",
    },
  },
  "values": {},
}
`;

exports[`joiResolver > should return all the errors from joiResolver when validation fails with \`validateAllFieldCriteria\` set to true 1`] = `
{
  "errors": {
    "birthYear": {
      "message": ""birthYear" must be a number",
      "ref": undefined,
      "type": "number.base",
      "types": {
        "number.base": ""birthYear" must be a number",
      },
    },
    "email": {
      "message": ""email" is not allowed to be empty",
      "ref": {
        "name": "email",
      },
      "type": "string.empty",
      "types": {
        "string.empty": ""email" is not allowed to be empty",
      },
    },
    "enabled": {
      "message": ""enabled" is required",
      "ref": undefined,
      "type": "any.required",
      "types": {
        "any.required": ""enabled" is required",
      },
    },
    "like": [
      {
        "id": {
          "message": ""like[0].id" must be a number",
          "ref": undefined,
          "type": "number.base",
          "types": {
            "number.base": ""like[0].id" must be a number",
          },
        },
        "name": {
          "message": ""like[0].name" length must be 4 characters long",
          "ref": undefined,
          "type": "string.length",
          "types": {
            "string.length": ""like[0].name" length must be 4 characters long",
            "string.pattern.base": ""like[0].name" with value "r" fails to match the required pattern: /a/",
          },
        },
      },
    ],
    "password": {
      "message": ""password" with value "___" fails to match the One uppercase character pattern",
      "ref": {
        "name": "password",
      },
      "type": "string.pattern.name",
      "types": {
        "string.min": ""password" length must be at least 8 characters long",
        "string.pattern.name": [
          ""password" with value "___" fails to match the One uppercase character pattern",
          ""password" with value "___" fails to match the One lowercase character pattern",
          ""password" with value "___" fails to match the One number pattern",
        ],
      },
    },
    "tags": {
      "message": ""tags" is required",
      "ref": undefined,
      "type": "any.required",
      "types": {
        "any.required": ""tags" is required",
      },
    },
    "username": {
      "message": ""username" is required",
      "ref": {
        "name": "username",
      },
      "type": "any.required",
      "types": {
        "any.required": ""username" is required",
      },
    },
  },
  "values": {},
}
`;

exports[`joiResolver > should return all the errors from joiResolver when validation fails with \`validateAllFieldCriteria\` set to true and \`mode: sync\` 1`] = `
{
  "errors": {
    "birthYear": {
      "message": ""birthYear" must be a number",
      "ref": undefined,
      "type": "number.base",
      "types": {
        "number.base": ""birthYear" must be a number",
      },
    },
    "email": {
      "message": ""email" is not allowed to be empty",
      "ref": {
        "name": "email",
      },
      "type": "string.empty",
      "types": {
        "string.empty": ""email" is not allowed to be empty",
      },
    },
    "enabled": {
      "message": ""enabled" is required",
      "ref": undefined,
      "type": "any.required",
      "types": {
        "any.required": ""enabled" is required",
      },
    },
    "like": [
      {
        "id": {
          "message": ""like[0].id" must be a number",
          "ref": undefined,
          "type": "number.base",
          "types": {
            "number.base": ""like[0].id" must be a number",
          },
        },
        "name": {
          "message": ""like[0].name" length must be 4 characters long",
          "ref": undefined,
          "type": "string.length",
          "types": {
            "string.length": ""like[0].name" length must be 4 characters long",
            "string.pattern.base": ""like[0].name" with value "r" fails to match the required pattern: /a/",
          },
        },
      },
    ],
    "password": {
      "message": ""password" with value "___" fails to match the One uppercase character pattern",
      "ref": {
        "name": "password",
      },
      "type": "string.pattern.name",
      "types": {
        "string.min": ""password" length must be at least 8 characters long",
        "string.pattern.name": [
          ""password" with value "___" fails to match the One uppercase character pattern",
          ""password" with value "___" fails to match the One lowercase character pattern",
          ""password" with value "___" fails to match the One number pattern",
        ],
      },
    },
    "tags": {
      "message": ""tags" is required",
      "ref": undefined,
      "type": "any.required",
      "types": {
        "any.required": ""tags" is required",
      },
    },
    "username": {
      "message": ""username" is required",
      "ref": {
        "name": "username",
      },
      "type": "any.required",
      "types": {
        "any.required": ""username" is required",
      },
    },
  },
  "values": {},
}
`;
