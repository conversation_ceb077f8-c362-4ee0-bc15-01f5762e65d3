package com.lumilove.controller;

import com.lumilove.dto.ChatRequest;
import com.lumilove.dto.ChatResponse;
import com.lumilove.entity.User;
import com.lumilove.service.ChatService;
import jakarta.validation.Valid;
import lombok.RequiredArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.springframework.http.MediaType;
import org.springframework.http.ResponseEntity;
import org.springframework.security.core.Authentication;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.servlet.mvc.method.annotation.SseEmitter;

import java.io.IOException;
import java.util.List;
import java.util.concurrent.CompletableFuture;

@Slf4j
@RestController
@RequestMapping("/chat")
@RequiredArgsConstructor
public class ChatController {

    private final ChatService chatService;

    @GetMapping("/history/{characterId}")
    public ResponseEntity<List<ChatResponse>> getChatHistory(
            @PathVariable Long characterId,
            @RequestParam(defaultValue = "20") int limit,
            Authentication authentication) {

        User user = (User) authentication.getPrincipal();
        List<ChatResponse> chatHistory = chatService.getChatHistory(user.getId(), characterId, limit);

        return ResponseEntity.ok(chatHistory);
    }

    @GetMapping("/recent")
    public ResponseEntity<List<Long>> getRecentChatCharacterIds(
            @RequestParam(defaultValue = "10") int limit,
            Authentication authentication) {

        User user = (User) authentication.getPrincipal();
        List<Long> characterIds = chatService.getRecentChatCharacterIds(user.getId(), limit);

        return ResponseEntity.ok(characterIds);
    }

    @PostMapping("/send")
    public ResponseEntity<String> sendMessage(
            @Valid @RequestBody ChatRequest chatRequest,
            Authentication authentication) {

        try {
            User user = (User) authentication.getPrincipal();
            String response = chatService.processChat(chatRequest, user.getId());

            return ResponseEntity.ok(response);

        } catch (RuntimeException e) {
            log.error("Error processing chat", e);
            return ResponseEntity.badRequest().body(e.getMessage());
        }
    }

    @PostMapping(value = "/stream", produces = MediaType.TEXT_EVENT_STREAM_VALUE)
    public SseEmitter streamChat(@Valid @RequestBody ChatRequest chatRequest,
                                Authentication authentication) {

        SseEmitter emitter = new SseEmitter(120_000L); // 2 minutes timeout
        User user = (User) authentication.getPrincipal();

        CompletableFuture.runAsync(() -> {
            try {
                // The quota check will be handled in chatService.processChat

                // Send typing indicator
                emitter.send(SseEmitter.event()
                        .name("typing")
                        .data("Character is typing..."));

                // Process chat and get response
                String response = chatService.processChat(chatRequest, user.getId());

                // Send response in chunks for streaming effect
                String[] words = response.split(" ");
                StringBuilder currentChunk = new StringBuilder();

                for (String word : words) {
                    currentChunk.append(word).append(" ");

                    if (currentChunk.length() > 20) { // Send chunk every ~20 characters
                        emitter.send(SseEmitter.event()
                                .name("message")
                                .data(currentChunk.toString()));
                        currentChunk = new StringBuilder();

                        // Small delay for streaming effect
                        Thread.sleep(100);
                    }
                }

                // Send remaining chunk
                if (currentChunk.length() > 0) {
                    emitter.send(SseEmitter.event()
                            .name("message")
                            .data(currentChunk.toString()));
                }

                // Send completion event
                emitter.send(SseEmitter.event()
                        .name("complete")
                        .data("Message complete"));

                emitter.complete();

            } catch (Exception e) {
                log.error("Error in streaming chat", e);
                try {
                    emitter.send(SseEmitter.event()
                            .name("error")
                            .data("An error occurred while processing your message"));
                } catch (IOException ioException) {
                    log.error("Error sending error message", ioException);
                }
                emitter.completeWithError(e);
            }
        });

        return emitter;
    }
}
